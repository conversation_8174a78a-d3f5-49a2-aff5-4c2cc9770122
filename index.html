<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Management Pricing Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .pricing-notice {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem auto 0;
            max-width: 600px;
            font-size: 1.1rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            gap: 2rem;
            align-items: center;
            justify-content: space-between;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        @media (max-width: 968px) {
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 1rem;
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .included-features {
            background: #e8f5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .included-features h3 {
            color: #2e7d32;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #1b5e20;
            font-size: 0.9rem;
        }
        
        .feature-item::before {
            content: "✓";
            font-weight: bold;
        }
        
        .server-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .server-services {
            margin-top: 1.5rem;
            border-top: 1px solid #e9ecef;
            padding-top: 1rem;
        }

        .server-services-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 0.5rem 0;
            margin-bottom: 1rem;
        }

        .server-services-header h4 {
            margin: 0;
            color: #667eea;
            font-size: 1.1rem;
        }

        .expand-icon {
            transition: transform 0.3s;
            color: #667eea;
            font-size: 1.2rem;
        }

        .expand-icon.expanded {
            transform: rotate(180deg);
        }

        .server-services-content {
            display: none;
        }

        .server-services-content.expanded {
            display: block;
        }

        .server-service-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e9ecef;
        }

        .website-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .website-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .website-header h3 {
            margin: 0;
            color: #667eea;
        }

        .website-domain-input {
            flex: 1;
            margin-right: 1rem;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .website-services {
            margin-top: 1rem;
            border-top: 1px solid #e9ecef;
            padding-top: 1rem;
        }

        .website-services-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 0.5rem 0;
            margin-bottom: 1rem;
        }

        .website-services-header h4 {
            margin: 0;
            color: #667eea;
            font-size: 1.1rem;
        }

        .website-services-content {
            display: none;
        }

        .website-services-content.expanded {
            display: block;
        }

        .website-service-item {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e9ecef;
        }

        .capacity-notice {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        .capacity-notice h4 {
            margin: 0 0 0.75rem 0;
            color: #856404;
            font-size: 1.1rem;
        }

        .capacity-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .capacity-rule {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 0.95rem;
        }

        .capacity-warning {
            background: #fff2f2;
            border-left: 4px solid #dc3545;
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 0.95rem;
        }

        .capacity-validation-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 4px;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }

        .pricing-disclaimer {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem auto;
            max-width: 1200px;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .disclaimer-icon {
            font-size: 2rem;
            flex-shrink: 0;
            margin-top: 0.25rem;
        }

        .disclaimer-content {
            flex: 1;
        }

        .disclaimer-content h3 {
            margin: 0 0 0.75rem 0;
            color: #0056b3;
            font-size: 1.2rem;
        }

        .disclaimer-content p {
            margin: 0;
            color: #004085;
            font-size: 1rem;
            line-height: 1.5;
        }

        .support-agreement {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem auto;
            max-width: 1200px;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .agreement-icon {
            font-size: 2rem;
            flex-shrink: 0;
            margin-top: 0.25rem;
        }

        .agreement-content {
            flex: 1;
        }

        .agreement-content h3 {
            margin: 0 0 1rem 0;
            color: #495057;
            font-size: 1.2rem;
        }

        .support-terms {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .support-term {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            border: 1px solid #dee2e6;
        }

        .support-term h4 {
            margin: 0 0 0.75rem 0;
            color: #495057;
            font-size: 1rem;
        }

        .support-term p {
            margin: 0;
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .support-scope {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 0.75rem;
        }

        .scope-section {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 0.75rem;
        }

        .scope-section h5 {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .scope-section ul {
            margin: 0;
            padding-left: 1.25rem;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .scope-section li {
            margin-bottom: 0.25rem;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .support-scope {
                grid-template-columns: 1fr;
            }
        }
        
        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .server-header h3 {
            color: #495057;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .remove-btn:hover {
            background: #c82333;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 0.25rem;
            color: #666;
            font-size: 0.875rem;
        }
        
        .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            background: white;
        }
        
        .ram-control-group {
            background: #f0f0f0;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .ram-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
        }
        
        .ram-btn {
            background: #667eea;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .ram-btn:hover {
            background: #5a67d8;
        }
        
        .ram-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .ram-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .ram-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .ram-value {
            font-weight: bold;
            color: #667eea;
            min-width: 60px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        .migration-checkbox {
            margin-top: 0.5rem;
        }
        
        .migration-checkbox label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .service-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .service-item.disabled {
            opacity: 0.5;
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .service-header input[type="checkbox"] {
            margin-right: 1rem;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name {
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
        }
        
        .service-price {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .service-controls {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            display: none;
        }

        .service-dependency {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #1976d2;
        }

        .service-dependency::before {
            content: "🔗 ";
            font-weight: bold;
        }
        
        .slider-control {
            margin-bottom: 1rem;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .quantity-btn {
            background: #667eea;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .quantity-btn:hover {
            background: #5a67d8;
        }

        .quantity-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .slider-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .slider-label span {
            color: #666;
            font-size: 0.9rem;
        }
        
        .slider-value {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
        }

        .price-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #495057;
        }

        .price-preview strong {
            color: #667eea;
        }
        
        .quantity-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .quantity-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .quantity-slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .add-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        
        .add-btn:hover {
            background: #5a67d8;
        }
        
        .pdf-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .pdf-btn:hover {
            background: #218838;
        }
        
        .summary {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            position: sticky;
            top: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .summary h3 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .summary-controls {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .toggle {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ddd;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle.active {
            background: #667eea;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle.active .toggle-slider {
            transform: translateX(26px);
        }
        
        .summary-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .summary-line {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .summary-line:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            padding-top: 1rem;
            border-top: 2px solid #667eea;
        }
        
        .vat-line {
            color: #666;
            font-size: 0.9rem;
            font-style: italic;
        }
        
        .highlight {
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-top: 1rem;
        }
        
        .discount-notice {
            background: #ffc107;
            color: #333;
            padding: 0.75rem;
            border-radius: 8px;
            text-align: center;
            margin-top: 1rem;
            font-weight: 500;
        }
        
        .lang-switch {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .lang-switch:hover {
            background: #667eea;
            color: white;
        }
        
        .lang-switch.active {
            background: #667eea;
            color: white;
        }
        
        .monitoring-features {
            margin-top: 1rem;
            padding: 1rem;
            background: #f0f0f0;
            border-radius: 6px;
        }
        
        .monitoring-feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .monitoring-feature input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
        
        .monitoring-feature label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .backup-options {
            margin-top: 1rem;
        }
        
        .backup-radio {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .backup-radio input[type="radio"] {
            width: 16px;
            height: 16px;
        }
        
        .backup-radio label {
            font-size: 0.9rem;
            color: #666;
            cursor: pointer;
        }
        
        @media print {
            body { background: white; }
            .controls { display: none; }
            .add-btn, .remove-btn, .pdf-btn { display: none; }
            .section { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 id="main-title">Server Management Pricing Calculator</h1>
        <p id="main-subtitle">Professional hosting management services</p>
        <div class="pricing-notice" id="pricing-notice">
            <strong>Base Price:</strong> €6 per GB RAM per month (€7.38 incl. VAT)
        </div>
    </div>

    <!-- Pricing Model Disclaimer -->
    <div class="pricing-disclaimer">
        <div class="disclaimer-icon">ℹ️</div>
        <div class="disclaimer-content">
            <h3 id="disclaimer-title">Pricing Model Notice</h3>
            <p id="disclaimer-text">All pricing is based on a <strong>"Bring Your Own Server"</strong> model. We do not provide hardware unless requested separately. Our services focus on professional server management, optimization, and support for your existing infrastructure.</p>
        </div>
    </div>

    <!-- Support Agreement Terms -->
    <div class="support-agreement">
        <div class="agreement-icon">📋</div>
        <div class="agreement-content">
            <h3 id="support-title">Support Agreement Terms</h3>
            <div class="support-terms">
                <div class="support-term">
                    <h4 id="support-coverage-title">24/7 Support Coverage</h4>
                    <p id="support-coverage-text">Our 24/7 support is provided for <strong>general technical issues</strong> related to server infrastructure, performance, and standard configurations. This does not include user-induced problems or custom development work.</p>
                </div>

                <div class="support-term">
                    <h4 id="support-billing-title">Billing for Extended Support</h4>
                    <p id="support-billing-text">For issues requiring more than <strong>30 minutes</strong> to resolve, clients will be notified of the estimated time and cost. The billing timer starts only after receiving your approval to proceed with extended troubleshooting.</p>
                </div>

                <div class="support-term">
                    <h4 id="support-scope-title">Support Scope Definition</h4>
                    <div class="support-scope">
                        <div class="scope-section">
                            <h5 id="general-technical-title">✅ General Technical Issues (Covered)</h5>
                            <ul id="general-technical-list">
                                <li>Server performance optimization</li>
                                <li>Infrastructure monitoring and alerts</li>
                                <li>Security patches and updates</li>
                                <li>Backup and restore operations</li>
                                <li>Standard configuration troubleshooting</li>
                                <li>Network connectivity issues</li>
                            </ul>
                        </div>

                        <div class="scope-section">
                            <h5 id="user-induced-title">❌ User-Induced Issues (Billable)</h5>
                            <ul id="user-induced-list">
                                <li>Custom code debugging and troubleshooting</li>
                                <li>Third-party plugin conflicts</li>
                                <li>Configuration changes requested by client</li>
                                <li>Data recovery from user errors</li>
                                <li>Performance issues caused by custom code</li>
                                <li>Training and consultation services</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <button class="lang-switch active" onclick="setLanguage('en')">EN</button>
                <button class="lang-switch" onclick="setLanguage('lt')">LT</button>
            </div>
            
            <button class="pdf-btn" onclick="generatePDF()">
                📄 <span id="pdf-btn-text">Save as PDF</span>
            </button>
        </div>
        
        <div class="pricing-grid">
            <div class="left-column">
                <div class="section">
                    <h2>🖥️ <span id="servers-title">Servers</span></h2>
                    
                    <div class="included-features">
                        <h3 id="included-title">Included with every server:</h3>
                        <div class="feature-list">
                            <div class="feature-item" id="feat-1">24/7 critical fault support</div>
                            <div class="feature-item" id="feat-2">Basic server & client cache</div>
                            <div class="feature-item" id="feat-3">7G WAF protection</div>
                            <div class="feature-item" id="feat-4">Basic bot protection</div>
                            <div class="feature-item" id="feat-5">Plugin vulnerability scans</div>
                            <div class="feature-item" id="feat-6">Daily offsite backups</div>
                            <div class="feature-item" id="feat-7">Staging sites</div>
                            <div class="feature-item" id="feat-8">Site cloning</div>
                            <div class="feature-item" id="feat-9">Server monitoring (CPU, RAM, Disk)</div>
                            <div class="feature-item" id="feat-10">Basic uptime monitoring per site</div>
                        </div>

                        <!-- Server Capacity Limitations -->
                        <div class="capacity-notice">
                            <h4 id="capacity-title">⚠️ Server Capacity Limitations</h4>
                            <div class="capacity-info">
                                <div class="capacity-rule" id="capacity-rule">
                                    <strong>Site limits apply:</strong> 5 production websites + 5 staging websites per 1GB of RAM
                                </div>
                                <div class="capacity-warning" id="capacity-warning">
                                    <strong>⚠️ High-resource websites:</strong> Ecommerce, LMS and other high CPU/high traffic websites should be hosted on their own servers to avoid migration complexity when servers need to be upgraded
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="servers-container"></div>
                    <button class="add-btn" onclick="addServer()" id="add-server-btn">+ Add Server</button>
                </div>
                
                <div class="section">
                    <h2>🌐 <span id="websites-title">Websites & Domains</span></h2>
                    <div id="websites-container">
                        <!-- Websites will be added here dynamically -->
                    </div>
                    <button class="add-btn" id="add-website-btn" onclick="addWebsite()">+ Add Website/Domain</button>
                </div>

                <div class="section">
                    <h2>🚀 <span id="oneoff-title">One-Time Services</span></h2>
                    
                    <!-- Speed Optimization -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="speed-opt" onchange="toggleService('speed-opt')">
                            <div class="service-info">
                                <div class="service-name" id="speed-opt-name">Website Speed Optimization</div>
                                <div class="service-price" id="speed-opt-price">€100 per website (€123 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="speed-opt-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="speed-opt-label">Number of websites</span>
                                    <span class="slider-value" id="speed-opt-value">1</span>
                                </div>
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="updateQuantity('speed-opt', -1)">-</button>
                                    <input type="range" class="quantity-slider" id="speed-opt-qty"
                                           min="1" max="50" value="1" oninput="updateServiceQuantity('speed-opt')">
                                    <button class="quantity-btn" onclick="updateQuantity('speed-opt', 1)">+</button>
                                </div>
                            </div>
                            <div class="price-preview" id="speed-opt-preview">
                                <strong>Total:</strong> €100 (€123 incl. VAT)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Re-optimization -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="reopt" onchange="toggleService('reopt')">
                            <div class="service-info">
                                <div class="service-name" id="reopt-name">Re-optimization</div>
                                <div class="service-price" id="reopt-price">€50 per website (€61.50 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="reopt-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="reopt-label">Number of websites</span>
                                    <span class="slider-value" id="reopt-value">1</span>
                                </div>
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="updateQuantity('reopt', -1)">-</button>
                                    <input type="range" class="quantity-slider" id="reopt-qty"
                                           min="1" max="50" value="1" oninput="updateServiceQuantity('reopt')">
                                    <button class="quantity-btn" onclick="updateQuantity('reopt', 1)">+</button>
                                </div>
                            </div>
                            <div class="price-preview" id="reopt-preview">
                                <strong>Total:</strong> €50 (€61.50 incl. VAT)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Extended Firewall -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="firewall" onchange="toggleService('firewall')">
                            <div class="service-info">
                                <div class="service-name" id="firewall-name">Extended Firewall (Cloudflare)</div>
                                <div class="service-price" id="firewall-price">€50 per website (€61.50 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="firewall-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="firewall-label">Number of websites</span>
                                    <span class="slider-value" id="firewall-value">1</span>
                                </div>
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="updateQuantity('firewall', -1)">-</button>
                                    <input type="range" class="quantity-slider" id="firewall-qty"
                                           min="1" max="50" value="1" oninput="updateServiceQuantity('firewall')">
                                    <button class="quantity-btn" onclick="updateQuantity('firewall', 1)">+</button>
                                </div>
                            </div>
                            <div class="price-preview" id="firewall-preview">
                                <strong>Total:</strong> €50 (€61.50 incl. VAT)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hourly Support -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="hourly-support" onchange="toggleService('hourly-support')">
                            <div class="service-info">
                                <div class="service-name" id="hourly-name">Hourly Support</div>
                                <div class="service-price" id="hourly-price">€100 per hour (€123 incl. VAT)</div>
                                <div class="service-description" style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;" id="hourly-description">Ad-hoc technical support outside of normal agreement (e.g., code troubleshooting, custom configurations)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="hourly-support-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="hourly-label">Number of hours</span>
                                    <span class="slider-value" id="hourly-support-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="hourly-support-qty"
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('hourly-support')">
                            </div>
                            <div class="price-preview" id="hourly-support-preview">
                                <strong>Total:</strong> €100 (€123 incl. VAT)
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>📅 <span id="monthly-title">Monthly Services</span></h2>
                    

                    <!-- 24/7 Support -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="support247" onchange="toggleService('support247')">
                            <div class="service-info">
                                <div class="service-name" id="support247-name">24/7 Mission Critical Support</div>
                                <div class="service-price" id="support247-price">€100 per site/month (€123 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="support247-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="support247-label">Number of websites</span>
                                    <span class="slider-value" id="support247-value">1</span>
                                </div>
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="updateQuantity('support247', -1)">-</button>
                                    <input type="range" class="quantity-slider" id="support247-qty"
                                           min="1" max="50" value="1" oninput="updateServiceQuantity('support247')">
                                    <button class="quantity-btn" onclick="updateQuantity('support247', 1)">+</button>
                                </div>
                            </div>
                            <div class="price-preview" id="support247-preview">
                                <strong>Monthly:</strong> €100 (€123 incl. VAT)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="right-column">
                <div class="summary" id="pricing-summary">
                    <h3 id="summary-title">💰 Pricing Summary</h3>
                    
                    <div class="summary-controls">
                        <div class="control-group">
                            <label id="vat-label-summary">Include VAT (23%)</label>
                            <div class="toggle" id="vat-toggle-summary" onclick="toggleVAT()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label id="billing-label-summary">Annual Billing (12% discount)</label>
                            <div class="toggle" id="billing-toggle-summary" onclick="toggleBilling()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4 id="setup-fees-title">One-Time Setup Fees</h4>
                        <div id="setup-breakdown"></div>
                        <div class="summary-line">
                            <strong id="total-setup-label">Total Setup</strong>
                            <strong id="total-setup">€0</strong>
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4 id="monthly-fees-title">Monthly Fees</h4>
                        <div id="monthly-breakdown"></div>
                        <div class="summary-line">
                            <strong id="total-monthly-label">Total Monthly</strong>
                            <strong id="total-monthly">€0</strong>
                        </div>
                    </div>
                    
                    <div class="summary-section" id="annual-totals-section" style="display: none;">
                        <h4 id="annual-totals-title">Annual Totals</h4>
                        <div class="summary-line">
                            <span id="annual-setup-label">Annual Setup</span>
                            <span id="annual-setup">€0</span>
                        </div>
                        <div class="summary-line">
                            <span id="annual-recurring-label">Annual Recurring</span>
                            <span id="annual-recurring">€0</span>
                        </div>
                        <div class="summary-line" style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid #e0e0e0;">
                            <strong id="total-annual-cost-label">Total Annual Cost</strong>
                            <strong id="total-annual-cost">€0</strong>
                        </div>
                        <div class="summary-line" style="color: #4CAF50;">
                            <span id="annual-savings-label">Annual Savings</span>
                            <span id="annual-savings-display">€0</span>
                        </div>
                    </div>
                    
                    <div class="highlight">
                        <div id="first-month-label">First Month Total</div>
                        <div style="font-size: 2rem; font-weight: bold;" id="first-month-total">€0</div>
                    </div>
                    
                    <div class="discount-notice" id="annual-discount" style="display: none;">
                        <span id="annual-text">Annual payment saves</span> <span id="annual-savings">€0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        // Translations
        const translations = {
            en: {
                mainTitle: "Server Management Pricing Calculator",
                mainSubtitle: "Professional hosting management services",
                pricingNotice: "Base Price: €6 per GB RAM per month (€7.38 incl. VAT)",
                disclaimerTitle: "Pricing Model Notice",
                disclaimerText: "All pricing is based on a \"Bring Your Own Server\" model. We do not provide hardware unless requested separately. Our services focus on professional server management, optimization, and support for your existing infrastructure.",
                supportTitle: "Support Agreement Terms",
                supportCoverageTitle: "24/7 Support Coverage",
                supportCoverageText: "Our 24/7 support is provided for general technical issues related to server infrastructure, performance, and standard configurations. This does not include user-induced problems or custom development work.",
                supportBillingTitle: "Billing for Extended Support",
                supportBillingText: "For issues requiring more than 30 minutes to resolve, clients will be notified of the estimated time and cost. The billing timer starts only after receiving your approval to proceed with extended troubleshooting.",
                supportScopeTitle: "Support Scope Definition",
                generalTechnicalTitle: "✅ General Technical Issues (Covered)",
                userInducedTitle: "❌ User-Induced Issues (Billable)",
                vatLabel: "Include VAT (23%)",
                billingLabel: "Annual Billing (12% discount)",
                migrationLabel: "Contract start migration (no setup fee)",
                serversTitle: "Servers",
                addServerBtn: "+ Add Server",
                serverLabel: "Server",
                websitesTitle: "Websites & Domains",
                addWebsiteBtn: "+ Add Website/Domain",
                websiteLabel: "Website",
                ramLabel: "RAM",
                cloudProviderLabel: "Cloud Provider",
                removeBtn: "Remove",
                oneoffTitle: "One-Time Services",
                monthlyTitle: "Monthly Services",
                summaryTitle: "💰 Pricing Summary",
                setupFeesTitle: "One-Time Setup Fees",
                monthlyFeesTitle: "Monthly Fees",
                totalSetupLabel: "Total Setup",
                totalMonthlyLabel: "Total Monthly",
                firstMonthLabel: "First Month Total",
                firstYearLabel: "First Year Total",
                annualText: "Annual payment saves",
                pdfBtnText: "Save as PDF",
                annualTotalsTitle: "Annual Totals",
                annualSetupLabel: "Annual Setup",
                annualRecurringLabel: "Annual Recurring",
                totalAnnualCostLabel: "Total Annual Cost",
                annualSavingsLabel: "Annual Savings",
                includedTitle: "Included with every server:",
                numberOfWebsites: "Number of websites",
                capacityTitle: "⚠️ Server Capacity Limitations",
                capacityRule: "Site limits apply: 5 production websites + 5 staging websites per 1GB of RAM",
                capacityWarning: "⚠️ High-resource websites: Ecommerce, LMS and other high CPU/high traffic websites should be hosted on their own servers to avoid migration complexity when servers need to be upgraded",
                
                // Features
                feat1: "24/7 critical fault support",
                feat2: "Basic server & client cache",
                feat3: "7G WAF protection",
                feat4: "Basic bot protection",
                feat5: "Plugin vulnerability scans",
                feat6: "Daily offsite backups",
                feat7: "Staging sites",
                feat8: "Site cloning",
                feat9: "Server monitoring (CPU, RAM, Disk)",
                feat10: "Basic uptime monitoring per site",
                
                // VAT display
                vatAmount: "VAT (23%)",
                exclVat: "Subtotal (excl. VAT)",
                
                // Services
                speedOptName: "Website Speed Optimization",
                speedOptPrice: "€100 per website (€123 incl. VAT)",
                reoptName: "Re-optimization",
                reoptPrice: "€50 per website (€61.50 incl. VAT)",
                firewallName: "Extended Firewall (Cloudflare)",
                firewallPrice: "€50 per website (€61.50 incl. VAT)",
                fortressName: "WP Fortress Security",
                fortressPrice: "€50 setup + €3/month per site (€61.50 setup + €3.69/month incl. VAT)",
                fortressSetupName: "WP Fortress Setup",
                fortressDependency: "Linked service: Setup fee and monthly fee quantities are automatically synchronized",
                extMonName: "Extended Monitoring",
                extMonPrice: "€2 per feature per site/month",
                monSitemapLabel: "Sitemap change monitoring",
                monDomLabel: "Page DOM change monitoring",
                monVisualLabel: "Page visual monitoring",
                backupName: "Hourly Offsite Backup",
                backupPrice: "Per site or BYO storage options",
                backupPersiteLabel: "Per site - €2/month per site (€2.46 incl. VAT)",
                backupByoLabel: "BYO Storage - €100 setup + €3/month per GB RAM (€123 setup + €3.69/month incl. VAT)",
                backupSetupName: "BYO Storage Backup Setup",
                backupDependency: "Linked service: BYO option pricing depends on total server RAM across all servers",
                support247Name: "24/7 Mission Critical Support",
                support247Price: "€100 per site/month (€123 incl. VAT)",
                hourlyName: "Hourly Support",
                hourlyPrice: "€100 per hour (€123 incl. VAT)",
                hourlyDescription: "Ad-hoc technical support outside of normal agreement (e.g., code troubleshooting, custom configurations)",
                hourlyLabel: "Number of hours",
                hour: "hour",
                hours: "hours",
                
                // Summary items
                serverManagement: "Server Management",
                serverSetup: "Server Setup",
                gbRam: "GB RAM",
                websites: "websites",
                sites: "sites",
                perMonth: "/month",
                complexityFee: "Complexity Fee",
                features: "features"
            },
            lt: {
                mainTitle: "Serverių Valdymo Kainų Skaičiuoklė",
                mainSubtitle: "Profesionalios hostingo valdymo paslaugos",
                pricingNotice: "Bazinė kaina: €6 už GB RAM per mėnesį (€7.38 su PVM)",
                disclaimerTitle: "Kainų modelio pranešimas",
                disclaimerText: "Visos kainos pagrįstos \"Atsineškite savo serverį\" modeliu. Mes neteikiame aparatūros, nebent būtų atskirai prašoma. Mūsų paslaugos orientuotos į profesionalų serverio valdymą, optimizavimą ir palaikymą jūsų esamai infrastruktūrai.",
                supportTitle: "Palaikymo sutarties sąlygos",
                supportCoverageTitle: "24/7 palaikymo aprėptis",
                supportCoverageText: "Mūsų 24/7 palaikymas teikiamas bendriems techniniams klausimams, susijusiems su serverio infrastruktūra, našumu ir standartinėmis konfigūracijomis. Tai neapima vartotojų sukeltų problemų ar individualaus programavimo darbų.",
                supportBillingTitle: "Išplėstinio palaikymo atsiskaitymas",
                supportBillingText: "Problemoms, kurių sprendimas užtrunka daugiau nei 30 minučių, klientai bus informuoti apie numatomą laiką ir kainą. Atsiskaitymo laikmatis pradedamas tik gavus jūsų sutikimą tęsti išplėstinį trikčių šalinimą.",
                supportScopeTitle: "Palaikymo aprėpties apibrėžimas",
                generalTechnicalTitle: "✅ Bendrieji techniniai klausimai (Dengiami)",
                userInducedTitle: "❌ Vartotojų sukeltos problemos (Apmokestinamos)",
                vatLabel: "Įtraukti PVM (23%)",
                billingLabel: "Metinis mokėjimas (12% nuolaida)",
                migrationLabel: "Sutarties pradžios migracija (be diegimo mokesčio)",
                serversTitle: "Serveriai",
                addServerBtn: "+ Pridėti serverį",
                serverLabel: "Serveris",
                websitesTitle: "Svetainės ir domenai",
                addWebsiteBtn: "+ Pridėti svetainę/domeną",
                websiteLabel: "Svetainė",
                ramLabel: "RAM",
                cloudProviderLabel: "Debesų tiekėjas",
                removeBtn: "Pašalinti",
                oneoffTitle: "Vienkartinės paslaugos",
                monthlyTitle: "Mėnesinės paslaugos",
                summaryTitle: "💰 Kainų suvestinė",
                setupFeesTitle: "Vienkartiniai mokesčiai",
                monthlyFeesTitle: "Mėnesiniai mokesčiai",
                totalSetupLabel: "Viso vienkartiniai",
                totalMonthlyLabel: "Viso per mėnesį",
                firstMonthLabel: "Pirmo mėnesio suma",
                firstYearLabel: "Pirmų metų suma",
                annualText: "Metinis mokėjimas sutaupo",
                pdfBtnText: "Išsaugoti PDF",
                annualTotalsTitle: "Metinės sumos",
                annualSetupLabel: "Metinis diegimas",
                annualRecurringLabel: "Metinė pasikartojanti suma",
                totalAnnualCostLabel: "Bendra metinė kaina",
                annualSavingsLabel: "Metinė nuolaida",
                includedTitle: "Įtraukta su kiekvienu serveriu:",
                numberOfWebsites: "Svetainių skaičius",
                capacityTitle: "⚠️ Serverio talpos apribojimai",
                capacityRule: "Svetainių limitai: 5 produkcijos svetainės + 5 bandymo svetainės vienam GB RAM",
                capacityWarning: "⚠️ Didelių išteklių svetainės: Elektroninės parduotuvės, mokymosi valdymo sistemos ir kitos didelio CPU/didelio srauto svetainės turėtų būti talpinamos atskiruose serveriuose, kad būtų išvengta migracijos sudėtingumo, kai serverius reikia atnaujinti",
                
                // Features
                feat1: "24/7 kritinių gedimų palaikymas",
                feat2: "Bazinis serverio ir kliento talpykla",
                feat3: "7G WAF apsauga",
                feat4: "Bazinė botų apsauga",
                feat5: "Įskiepių pažeidžiamumo nuskaitymai",
                feat6: "Kasdieninės atsarginės kopijos",
                feat7: "Bandymo svetainės",
                feat8: "Svetainių klonavimas",
                feat9: "Serverio stebėjimas (CPU, RAM, Diskas)",
                feat10: "Bazinis veikimo laiko stebėjimas",
                
                // VAT display
                vatAmount: "PVM (23%)",
                exclVat: "Tarpinė suma (be PVM)",
                
                // Services
                speedOptName: "Svetainės greičio optimizavimas",
                speedOptPrice: "€100 už svetainę (€123 su PVM)",
                reoptName: "Pakartotinis optimizavimas",
                reoptPrice: "€50 už svetainę (€61.50 su PVM)",
                firewallName: "Išplėstinė ugniasienė (Cloudflare)",
                firewallPrice: "€50 už svetainę (€61.50 su PVM)",
                fortressName: "WP Fortress apsauga",
                fortressPrice: "€50 diegimas + €3/mėn už svetainę (€61.50 diegimas + €3.69/mėn su PVM)",
                fortressSetupName: "WP Fortress diegimas",
                fortressDependency: "Susieta paslauga: Diegimo ir mėnesinio mokesčio kiekiai automatiškai sinchronizuojami",
                extMonName: "Išplėstinis stebėjimas",
                extMonPrice: "€2 už funkciją už svetainę/mėn",
                monSitemapLabel: "Svetainės žemėlapio pokyčių stebėjimas",
                monDomLabel: "Puslapio DOM pokyčių stebėjimas",
                monVisualLabel: "Puslapio vizualinis stebėjimas",
                backupName: "Valandinės atsarginės kopijos",
                backupPrice: "Už svetainę arba BYO saugyklos parinktys",
                backupPersiteLabel: "Už svetainę - €2/mėn už svetainę (€2.46 su PVM)",
                backupByoLabel: "BYO saugykla - €100 diegimas + €3/mėn už GB RAM (€123 diegimas + €3.69/mėn su PVM)",
                backupSetupName: "BYO saugyklos atsarginių kopijų diegimas",
                backupDependency: "Susieta paslauga: BYO pasirinkimo kaina priklauso nuo bendro serverių RAM kiekio",
                support247Name: "24/7 kritinių situacijų palaikymas",
                support247Price: "€100 už svetainę/mėn (€123 su PVM)",
                hourlyName: "Valandinis palaikymas",
                hourlyPrice: "€100 už valandą (€123 su PVM)",
                hourlyDescription: "Papildomas techninis palaikymas, neįtrauktas į standartinę sutartį (pvz., kodo trikčių šalinimas, individualios konfigūracijos)",
                hourlyLabel: "Valandų skaičius",
                hour: "valanda",
                hours: "valandos",
                
                // Summary items
                serverManagement: "Serverių valdymas",
                serverSetup: "Serverio diegimas",
                gbRam: "GB RAM",
                websites: "svetainės",
                sites: "svetainės",
                perMonth: "/mėn",
                complexityFee: "Sudėtingumo mokestis",
                features: "funkcijos"
            }
        };
        
        let currentLang = 'en';
        let includeVAT = false;
        let annualBilling = false;
        let serverCount = 0;
        let websiteCount = 0;
        
        const VAT_RATE = 0.23;
        const ANNUAL_DISCOUNT = 0.12;
        const RAM_PRICE = 6; // €6 per GB RAM
        
        const cloudProviders = {
            'vultr': { name: 'Vultr', extraFee: 0 },
            'digitalocean': { name: 'Digital Ocean', extraFee: 0 },
            'upcloud': { name: 'UpCloud', extraFee: 0 },
            'hetzner': { name: 'Hetzner', extraFee: 0 },
            'linode': { name: 'Linode', extraFee: 0 },
            'aws': { name: 'AWS', extraFee: 50 },
            'gcloud': { name: 'Google Cloud', extraFee: 50 }
        };
        
        function setLanguage(lang) {
            currentLang = lang;
            document.querySelectorAll('.lang-switch').forEach(btn => {
                btn.classList.toggle('active', btn.textContent.toLowerCase() === lang);
            });
            updateAllText();
            updateSummary();
        }
        
        function formatPrice(amount) {
            return `€${amount.toFixed(2)}`;
        }
        
        function updateAllText() {
            const t = translations[currentLang];
            
            // Update static text
            document.getElementById('main-title').textContent = t.mainTitle;
            document.getElementById('main-subtitle').textContent = t.mainSubtitle;
            document.getElementById('pricing-notice').innerHTML = `<strong>${t.pricingNotice.split(':')[0]}:</strong> ${t.pricingNotice.split(':')[1]}`;
            document.getElementById('disclaimer-title').textContent = t.disclaimerTitle;
            document.getElementById('disclaimer-text').innerHTML = t.disclaimerText;
            document.getElementById('support-title').textContent = t.supportTitle;
            document.getElementById('support-coverage-title').textContent = t.supportCoverageTitle;
            document.getElementById('support-coverage-text').innerHTML = t.supportCoverageText;
            document.getElementById('support-billing-title').textContent = t.supportBillingTitle;
            document.getElementById('support-billing-text').innerHTML = t.supportBillingText;
            document.getElementById('support-scope-title').textContent = t.supportScopeTitle;
            document.getElementById('general-technical-title').textContent = t.generalTechnicalTitle;
            document.getElementById('user-induced-title').textContent = t.userInducedTitle;
            document.getElementById('vat-label-summary').textContent = t.vatLabel;
            document.getElementById('billing-label-summary').textContent = t.billingLabel;
            document.getElementById('servers-title').textContent = t.serversTitle;
            document.getElementById('add-server-btn').textContent = t.addServerBtn;
            document.getElementById('websites-title').textContent = t.websitesTitle;
            document.getElementById('add-website-btn').textContent = t.addWebsiteBtn;
            document.getElementById('oneoff-title').textContent = t.oneoffTitle;
            document.getElementById('monthly-title').textContent = t.monthlyTitle;
            document.getElementById('summary-title').textContent = t.summaryTitle;
            document.getElementById('setup-fees-title').textContent = t.setupFeesTitle;
            document.getElementById('monthly-fees-title').textContent = t.monthlyFeesTitle;
            document.getElementById('total-setup-label').textContent = t.totalSetupLabel;
            document.getElementById('total-monthly-label').textContent = t.totalMonthlyLabel;
            document.getElementById('first-month-label').textContent = t.firstMonthLabel;
            document.getElementById('annual-text').textContent = t.annualText;
            document.getElementById('pdf-btn-text').textContent = t.pdfBtnText;
            
            // Annual totals labels
            document.getElementById('annual-totals-title').textContent = t.annualTotalsTitle;
            document.getElementById('annual-setup-label').textContent = t.annualSetupLabel;
            document.getElementById('annual-recurring-label').textContent = t.annualRecurringLabel;
            document.getElementById('total-annual-cost-label').textContent = t.totalAnnualCostLabel;
            document.getElementById('annual-savings-label').textContent = t.annualSavingsLabel;
            document.getElementById('included-title').textContent = t.includedTitle;
            document.getElementById('capacity-title').textContent = t.capacityTitle;
            document.getElementById('capacity-rule').innerHTML = `<strong>Site limits apply:</strong> ${t.capacityRule.split(': ')[1]}`;
            document.getElementById('capacity-warning').innerHTML = `<strong>⚠️ High-resource websites:</strong> ${t.capacityWarning.split(': ')[1]}`;

            // Update features
            for (let i = 1; i <= 10; i++) {
                document.getElementById(`feat-${i}`).textContent = t[`feat${i}`];
            }
            
            // Update service names and prices
            document.getElementById('speed-opt-name').textContent = t.speedOptName;
            document.getElementById('speed-opt-price').textContent = t.speedOptPrice;
            document.getElementById('speed-opt-label').textContent = t.numberOfWebsites;
            
            document.getElementById('reopt-name').textContent = t.reoptName;
            document.getElementById('reopt-price').textContent = t.reoptPrice;
            document.getElementById('reopt-label').textContent = t.numberOfWebsites;
            
            document.getElementById('firewall-name').textContent = t.firewallName;
            document.getElementById('firewall-price').textContent = t.firewallPrice;
            document.getElementById('firewall-label').textContent = t.numberOfWebsites;
            
            document.getElementById('hourly-name').textContent = t.hourlyName;
            document.getElementById('hourly-price').textContent = t.hourlyPrice;
            document.getElementById('hourly-description').textContent = t.hourlyDescription;
            document.getElementById('hourly-label').textContent = t.hourlyLabel;
            
            document.getElementById('fortress-name').textContent = t.fortressName;
            document.getElementById('fortress-price').textContent = t.fortressPrice;
            document.getElementById('fortress-label').textContent = t.numberOfWebsites;
            document.getElementById('fortress-dependency').textContent = t.fortressDependency;
            
            document.getElementById('ext-mon-name').textContent = t.extMonName;
            document.getElementById('ext-mon-price').textContent = t.extMonPrice;
            document.getElementById('ext-mon-label').textContent = t.numberOfWebsites;
            document.getElementById('mon-sitemap-label').textContent = t.monSitemapLabel;
            document.getElementById('mon-dom-label').textContent = t.monDomLabel;
            document.getElementById('mon-visual-label').textContent = t.monVisualLabel;
            
            document.getElementById('backup-name').textContent = t.backupName;
            document.getElementById('backup-price').textContent = t.backupPrice;
            document.getElementById('backup-persite-label').textContent = t.backupPersiteLabel;
            document.getElementById('backup-byo-label').textContent = t.backupByoLabel;
            document.getElementById('backup-site-label').textContent = t.numberOfWebsites;
            document.getElementById('backup-dependency').textContent = t.backupDependency;
            
            document.getElementById('support247-name').textContent = t.support247Name;
            document.getElementById('support247-price').textContent = t.support247Price;
            document.getElementById('support247-label').textContent = t.numberOfWebsites;
            
            // Update server labels
            document.querySelectorAll('.server-item').forEach((server, index) => {
                server.querySelector('h3').textContent = `${t.serverLabel} ${index + 1}`;
                server.querySelector('.ram-label').textContent = t.ramLabel;
                server.querySelector('.provider-label').textContent = t.cloudProviderLabel;
                server.querySelector('.migration-label').textContent = t.migrationLabel;
                const removeBtn = server.querySelector('.remove-btn');
                if (removeBtn) removeBtn.textContent = t.removeBtn;
            });
        }
        
        function toggleVAT() {
            includeVAT = !includeVAT;
            document.getElementById('vat-toggle-summary').classList.toggle('active', includeVAT);
            updateSummary();
        }
        
        function toggleBilling() {
            annualBilling = !annualBilling;
            document.getElementById('billing-toggle-summary').classList.toggle('active', annualBilling);
            document.getElementById('annual-discount').style.display = annualBilling ? 'block' : 'none';
            updateSummary();
        }
        
        function addServer() {
            serverCount++;
            const container = document.getElementById('servers-container');
            const serverDiv = document.createElement('div');
            serverDiv.className = 'server-item';
            serverDiv.id = `server-${serverCount}`;
            
            const t = translations[currentLang];
            
            serverDiv.innerHTML = `
                <div class="server-header">
                    <h3>${t.serverLabel} ${serverCount}</h3>
                    ${serverCount > 1 ? `<button class="remove-btn" onclick="removeServer(${serverCount})">${t.removeBtn}</button>` : ''}
                </div>
                <div class="input-group">
                    <label class="provider-label">${t.cloudProviderLabel}</label>
                    <select id="provider-${serverCount}" onchange="updateSummary()">
                        ${Object.entries(cloudProviders).map(([key, provider]) =>
                            `<option value="${key}">${provider.name}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="ram-control-group">
                    <label class="ram-label">${t.ramLabel}</label>
                    <div class="ram-controls">
                        <button class="ram-btn" onclick="updateRAM(${serverCount}, -1)">-</button>
                        <input type="range" class="ram-slider" id="ram-slider-${serverCount}"
                               min="1" max="64" value="4" oninput="updateRAMDisplay(${serverCount})">
                        <button class="ram-btn" onclick="updateRAM(${serverCount}, 1)">+</button>
                        <div class="ram-value" id="ram-value-${serverCount}">4 GB</div>
                    </div>
                </div>
                <div class="migration-checkbox">
                    <label>
                        <input type="checkbox" id="migration-${serverCount}" onchange="updateSummary()">
                        <span class="migration-label">${t.migrationLabel}</span>
                    </label>
                </div>

                <!-- Server-specific services -->
                <div class="server-services">
                    <div class="server-services-header" onclick="toggleServerServices(${serverCount})">
                        <h4>Server Services</h4>
                        <span class="expand-icon" id="expand-icon-${serverCount}">▼</span>
                    </div>
                    <div class="server-services-content" id="server-services-${serverCount}">

                        <!-- WP Fortress Security -->
                        <div class="server-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="fortress-${serverCount}" onchange="toggleServerService('fortress', ${serverCount})">
                                <div class="service-info">
                                    <div class="service-name">WP Fortress Security</div>
                                    <div class="service-price">€50 setup + €3/month per site (€61.50 setup + €3.69/month incl. VAT)</div>
                                </div>
                            </div>
                            <div class="service-controls" id="fortress-${serverCount}-controls" style="display: none;">
                                <div class="service-dependency">
                                    🔗 Linked service: Setup fee and monthly fee quantities are automatically synchronized
                                </div>
                                <div class="slider-control">
                                    <div class="slider-label">
                                        <span>Number of websites</span>
                                        <span class="slider-value" id="fortress-${serverCount}-value">1</span>
                                    </div>
                                    <div class="quantity-controls">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('fortress', ${serverCount}, -1)">-</button>
                                        <input type="range" class="quantity-slider" id="fortress-${serverCount}-qty"
                                               min="1" max="50" value="1" oninput="updateServerServiceQuantity('fortress', ${serverCount}, 0)">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('fortress', ${serverCount}, 1)">+</button>
                                    </div>
                                </div>
                                <div class="price-preview" id="fortress-${serverCount}-preview">
                                    <strong>Setup:</strong> €50 (€61.50 incl. VAT)<br>
                                    <strong>Monthly:</strong> €3 (€3.69 incl. VAT)
                                </div>
                            </div>
                        </div>

                        <!-- Extended Monitoring -->
                        <div class="server-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="ext-mon-${serverCount}" onchange="toggleServerService('ext-mon', ${serverCount})">
                                <div class="service-info">
                                    <div class="service-name">Extended Monitoring</div>
                                    <div class="service-price">€2 per feature per site/month</div>
                                </div>
                            </div>
                            <div class="service-controls" id="ext-mon-${serverCount}-controls" style="display: none;">
                                <div class="slider-control">
                                    <div class="slider-label">
                                        <span>Number of websites</span>
                                        <span class="slider-value" id="ext-mon-${serverCount}-value">1</span>
                                    </div>
                                    <div class="quantity-controls">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('ext-mon', ${serverCount}, -1)">-</button>
                                        <input type="range" class="quantity-slider" id="ext-mon-${serverCount}-qty"
                                               min="1" max="50" value="1" oninput="updateServerServiceQuantity('ext-mon', ${serverCount}, 0)">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('ext-mon', ${serverCount}, 1)">+</button>
                                    </div>
                                </div>
                                <div class="monitoring-features">
                                    <div class="monitoring-feature">
                                        <input type="checkbox" id="mon-sitemap-${serverCount}" onchange="updateServerServicePreview('ext-mon', ${serverCount})">
                                        <label for="mon-sitemap-${serverCount}">Sitemap change monitoring</label>
                                    </div>
                                    <div class="monitoring-feature">
                                        <input type="checkbox" id="mon-dom-${serverCount}" onchange="updateServerServicePreview('ext-mon', ${serverCount})">
                                        <label for="mon-dom-${serverCount}">Page DOM change monitoring</label>
                                    </div>
                                    <div class="monitoring-feature">
                                        <input type="checkbox" id="mon-visual-${serverCount}" onchange="updateServerServicePreview('ext-mon', ${serverCount})">
                                        <label for="mon-visual-${serverCount}">Page visual monitoring</label>
                                    </div>
                                </div>
                                <div class="price-preview" id="ext-mon-${serverCount}-preview">
                                    <strong>Monthly:</strong> €0 (€0 incl. VAT) - Select features above
                                </div>
                            </div>
                        </div>

                        <!-- Backup Services -->
                        <div class="server-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="backup-${serverCount}" onchange="toggleServerService('backup', ${serverCount})">
                                <div class="service-info">
                                    <div class="service-name">Hourly Offsite Backup</div>
                                    <div class="service-price">Per site or BYO storage options</div>
                                </div>
                            </div>
                            <div class="service-controls" id="backup-${serverCount}-controls" style="display: none;">
                                <div class="service-dependency" id="backup-${serverCount}-dependency" style="display: none;">
                                    🔗 Linked service: BYO option pricing depends on total server RAM across all servers
                                </div>
                                <div class="backup-options">
                                    <div class="backup-radio">
                                        <input type="radio" id="backup-persite-${serverCount}" name="backup-type-${serverCount}" value="persite" checked onchange="updateServerBackupType(${serverCount})">
                                        <label for="backup-persite-${serverCount}">Per site - €2/month per site (€2.46 incl. VAT)</label>
                                    </div>
                                    <div class="backup-radio">
                                        <input type="radio" id="backup-byo-${serverCount}" name="backup-type-${serverCount}" value="byo" onchange="updateServerBackupType(${serverCount})">
                                        <label for="backup-byo-${serverCount}">BYO Storage - €100 setup + €3/month per GB RAM (€123 setup + €3.69/month incl. VAT)</label>
                                    </div>
                                </div>
                                <div class="slider-control" id="backup-site-control-${serverCount}">
                                    <div class="slider-label">
                                        <span>Number of websites</span>
                                        <span class="slider-value" id="backup-${serverCount}-value">1</span>
                                    </div>
                                    <div class="quantity-controls">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('backup', ${serverCount}, -1)">-</button>
                                        <input type="range" class="quantity-slider" id="backup-${serverCount}-qty"
                                               min="1" max="50" value="1" oninput="updateServerServiceQuantity('backup', ${serverCount}, 0)">
                                        <button class="quantity-btn" onclick="updateServerServiceQuantity('backup', ${serverCount}, 1)">+</button>
                                    </div>
                                </div>
                                <div class="price-preview" id="backup-${serverCount}-preview">
                                    <strong>Monthly:</strong> €2 (€2.46 incl. VAT)
                                </div>
                            </div>
                        </div>

                        <!-- Server Failover Setup -->
                        <div class="server-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="failover-${serverCount}" onchange="toggleServerService('failover', ${serverCount})">
                                <div class="service-info">
                                    <div class="service-name">Server Failover Setup</div>
                                    <div class="service-price">€50 setup + €10/month per server (€61.50 setup + €12.30/month incl. VAT)</div>
                                </div>
                            </div>
                            <div class="service-controls" id="failover-${serverCount}-controls" style="display: none;">
                                <div class="price-preview" id="failover-${serverCount}-preview">
                                    <strong>Setup:</strong> €50 (€61.50 incl. VAT)<br>
                                    <strong>Monthly:</strong> €10 (€12.30 incl. VAT)
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            `;
            
            container.appendChild(serverDiv);
            updateSummary();
        }
        
        function updateRAM(serverId, change) {
            const slider = document.getElementById(`ram-slider-${serverId}`);
            const newValue = parseInt(slider.value) + change;
            if (newValue >= 1 && newValue <= 64) {
                slider.value = newValue;
                updateRAMDisplay(serverId);
            }
        }
        
        function updateRAMDisplay(serverId) {
            const slider = document.getElementById(`ram-slider-${serverId}`);
            const value = document.getElementById(`ram-value-${serverId}`);
            value.textContent = `${slider.value} GB`;

            // Update backup preview if BYO is selected
            if (document.getElementById('backup') && document.getElementById('backup').checked) {
                const backupType = document.querySelector('input[name="backup-type"]:checked').value;
                if (backupType === 'byo') {
                    updateBackupPreview();
                }
            }

            updateSummary();
            validateServerCapacity();
        }
        
        function removeServer(id) {
            document.getElementById(`server-${id}`).remove();
            updateSummary();
            validateServerCapacity();
        }

        function toggleServerServices(serverId) {
            const content = document.getElementById(`server-services-${serverId}`);
            const icon = document.getElementById(`expand-icon-${serverId}`);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }

        function toggleServerService(serviceType, serverId) {
            const checkbox = document.getElementById(`${serviceType}-${serverId}`);
            const controls = document.getElementById(`${serviceType}-${serverId}-controls`);

            if (checkbox.checked) {
                controls.style.display = 'block';
                updateServerServicePreview(serviceType, serverId);
            } else {
                controls.style.display = 'none';
            }

            updateSummary();
        }

        function updateServerServiceQuantity(serviceType, serverId, change) {
            const slider = document.getElementById(`${serviceType}-${serverId}-qty`);
            const valueDisplay = document.getElementById(`${serviceType}-${serverId}-value`);

            if (change !== 0) {
                const currentValue = parseInt(slider.value);
                const minValue = parseInt(slider.min);
                const maxValue = parseInt(slider.max);
                const newValue = currentValue + change;

                if (newValue >= minValue && newValue <= maxValue) {
                    slider.value = newValue;
                }
            }

            const qty = parseInt(slider.value);
            valueDisplay.textContent = qty;

            updateServerServicePreview(serviceType, serverId);
            updateSummary();
        }

        function updateServerServicePreview(serviceType, serverId) {
            const preview = document.getElementById(`${serviceType}-${serverId}-preview`);
            if (!preview) return;

            const qty = parseInt(document.getElementById(`${serviceType}-${serverId}-qty`).value) || 1;

            switch(serviceType) {
                case 'fortress': {
                    const setupPrice = 50 * qty;
                    const monthlyPrice = 3 * qty;
                    preview.innerHTML = `<strong>Setup:</strong> €${setupPrice} (€${(setupPrice * 1.23).toFixed(2)} incl. VAT)<br>
                                        <strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                }

                case 'ext-mon': {
                    let features = 0;
                    if (document.getElementById(`mon-sitemap-${serverId}`).checked) features++;
                    if (document.getElementById(`mon-dom-${serverId}`).checked) features++;
                    if (document.getElementById(`mon-visual-${serverId}`).checked) features++;

                    const monitoringPrice = 2 * features * qty;
                    if (features === 0) {
                        preview.innerHTML = `<strong>Monthly:</strong> €0 (€0 incl. VAT) - Select features above`;
                    } else {
                        preview.innerHTML = `<strong>Monthly:</strong> €${monitoringPrice} (€${(monitoringPrice * 1.23).toFixed(2)} incl. VAT) - ${features} feature(s) × ${qty} site(s)`;
                    }
                    break;
                }

                case 'backup':
                    updateServerBackupPreview(serverId);
                    break;

                case 'failover': {
                    const setupPrice = 50;
                    const monthlyPrice = 10;
                    preview.innerHTML = `<strong>Setup:</strong> €${setupPrice} (€${(setupPrice * 1.23).toFixed(2)} incl. VAT)<br>
                                        <strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                }
            }
        }

        function updateServerBackupType(serverId) {
            const backupType = document.querySelector(`input[name="backup-type-${serverId}"]:checked`).value;
            const siteControl = document.getElementById(`backup-site-control-${serverId}`);
            const dependency = document.getElementById(`backup-${serverId}-dependency`);

            siteControl.style.display = backupType === 'persite' ? 'block' : 'none';
            dependency.style.display = backupType === 'byo' ? 'block' : 'none';

            updateServerBackupPreview(serverId);
            updateSummary();
        }

        function updateServerBackupPreview(serverId) {
            const preview = document.getElementById(`backup-${serverId}-preview`);
            const backupType = document.querySelector(`input[name="backup-type-${serverId}"]:checked`).value;

            if (backupType === 'persite') {
                const qty = parseInt(document.getElementById(`backup-${serverId}-qty`).value) || 1;
                const monthlyPrice = 2 * qty;
                preview.innerHTML = `<strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
            } else {
                // BYO - calculate based on this server's RAM
                const ram = parseFloat(document.getElementById(`ram-slider-${serverId}`).value) || 0;
                const setupPrice = 100;
                const monthlyPrice = 3 * ram;
                preview.innerHTML = `<strong>Setup:</strong> €${setupPrice} (€${(setupPrice * 1.23).toFixed(2)} incl. VAT)<br>
                                    <strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT) - ${ram} GB RAM`;
            }
        }

        // Server Capacity Validation
        function validateServerCapacity() {
            let totalRAM = 0;
            let totalWebsites = 0;
            let validationErrors = [];

            // Calculate total RAM across all servers
            document.querySelectorAll('.server-item').forEach(server => {
                const id = server.id.split('-')[1];
                const ram = parseFloat(document.getElementById(`ram-slider-${id}`).value) || 0;
                totalRAM += ram;
            });

            // Count total websites
            totalWebsites = document.querySelectorAll('.website-item').length;

            // Calculate capacity (5 production + 5 staging = 10 sites per GB RAM)
            const maxWebsites = totalRAM * 10;

            // Check if exceeding capacity
            if (totalWebsites > maxWebsites) {
                validationErrors.push(`Capacity exceeded: ${totalWebsites} websites configured, but only ${maxWebsites} websites allowed for ${totalRAM}GB RAM (5 production + 5 staging per GB)`);
            }

            // Display validation errors
            displayCapacityValidation(validationErrors);

            return validationErrors.length === 0;
        }

        function displayCapacityValidation(errors) {
            // Remove existing validation messages
            document.querySelectorAll('.capacity-validation-error').forEach(el => el.remove());

            if (errors.length > 0) {
                const capacityNotice = document.querySelector('.capacity-notice');
                errors.forEach(error => {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'capacity-validation-error';
                    errorDiv.textContent = error;
                    capacityNotice.appendChild(errorDiv);
                });
            }
        }

        // Website Management Functions
        function addWebsite() {
            websiteCount++;
            const container = document.getElementById('websites-container');
            const websiteDiv = document.createElement('div');
            websiteDiv.className = 'website-item';
            websiteDiv.id = `website-${websiteCount}`;

            const t = translations[currentLang];

            websiteDiv.innerHTML = `
                <div class="website-header">
                    <h3>Website ${websiteCount}</h3>
                    <input type="text" class="website-domain-input" id="domain-${websiteCount}"
                           placeholder="Enter domain name (e.g., example.com)" onchange="updateSummary()">
                    ${websiteCount > 1 ? `<button class="remove-btn" onclick="removeWebsite(${websiteCount})">Remove</button>` : ''}
                </div>

                <!-- Website-specific services -->
                <div class="website-services">
                    <div class="website-services-header" onclick="toggleWebsiteServices(${websiteCount})">
                        <h4>Website Services</h4>
                        <span class="expand-icon" id="website-expand-icon-${websiteCount}">▼</span>
                    </div>
                    <div class="website-services-content" id="website-services-${websiteCount}">

                        <!-- Speed Optimization -->
                        <div class="website-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="speed-opt-${websiteCount}" onchange="toggleWebsiteService('speed-opt', ${websiteCount})">
                                <div class="service-info">
                                    <div class="service-name">Website Speed Optimization</div>
                                    <div class="service-price">€100 per website (€123 incl. VAT)</div>
                                </div>
                            </div>
                            <div class="service-controls" id="speed-opt-${websiteCount}-controls" style="display: none;">
                                <div class="price-preview" id="speed-opt-${websiteCount}-preview">
                                    <strong>Total:</strong> €100 (€123 incl. VAT)
                                </div>
                            </div>
                        </div>

                        <!-- Re-optimization -->
                        <div class="website-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="reopt-${websiteCount}" onchange="toggleWebsiteService('reopt', ${websiteCount})">
                                <div class="service-info">
                                    <div class="service-name">Re-optimization</div>
                                    <div class="service-price">€50 per website (€61.50 incl. VAT)</div>
                                </div>
                            </div>
                            <div class="service-controls" id="reopt-${websiteCount}-controls" style="display: none;">
                                <div class="price-preview" id="reopt-${websiteCount}-preview">
                                    <strong>Total:</strong> €50 (€61.50 incl. VAT)
                                </div>
                            </div>
                        </div>

                        <!-- Extended Firewall -->
                        <div class="website-service-item">
                            <div class="service-header">
                                <input type="checkbox" id="firewall-${websiteCount}" onchange="toggleWebsiteService('firewall', ${websiteCount})">
                                <div class="service-info">
                                    <div class="service-name">Extended Firewall (Cloudflare)</div>
                                    <div class="service-price">€50 per website (€61.50 incl. VAT)</div>
                                </div>
                            </div>
                            <div class="service-controls" id="firewall-${websiteCount}-controls" style="display: none;">
                                <div class="price-preview" id="firewall-${websiteCount}-preview">
                                    <strong>Total:</strong> €50 (€61.50 incl. VAT)
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            `;

            container.appendChild(websiteDiv);
            updateSummary();
            validateServerCapacity();
        }

        function removeWebsite(id) {
            document.getElementById(`website-${id}`).remove();
            updateSummary();
            validateServerCapacity();
        }

        function toggleWebsiteServices(websiteId) {
            const content = document.getElementById(`website-services-${websiteId}`);
            const icon = document.getElementById(`website-expand-icon-${websiteId}`);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }

        function toggleWebsiteService(serviceType, websiteId) {
            const checkbox = document.getElementById(`${serviceType}-${websiteId}`);
            const controls = document.getElementById(`${serviceType}-${websiteId}-controls`);

            if (checkbox.checked) {
                controls.style.display = 'block';
            } else {
                controls.style.display = 'none';
            }

            updateSummary();
        }
        
        function toggleService(serviceId) {
            const checked = document.getElementById(serviceId).checked;
            const controls = document.getElementById(`${serviceId}-controls`);
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById(serviceId).closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleFortress() {
            const checked = document.getElementById('fortress').checked;
            const controls = document.getElementById('fortress-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById('fortress').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleMonitoring() {
            const checked = document.getElementById('extended-monitoring').checked;
            const controls = document.getElementById('ext-mon-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            if (!checked) {
                document.getElementById('mon-sitemap').checked = false;
                document.getElementById('mon-dom').checked = false;
                document.getElementById('mon-visual').checked = false;
            }
            
            // Update disabled state
            const serviceItem = document.getElementById('extended-monitoring').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleBackup() {
            const checked = document.getElementById('backup').checked;
            const controls = document.getElementById('backup-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById('backup').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function updateBackupType() {
            const backupType = document.querySelector('input[name="backup-type"]:checked').value;
            const siteControl = document.getElementById('backup-site-control');
            const dependency = document.getElementById('backup-dependency');

            siteControl.style.display = backupType === 'persite' ? 'block' : 'none';

            // Update dependency message based on backup type
            const t = translations[currentLang];
            if (backupType === 'byo') {
                dependency.textContent = t.backupDependency;
                dependency.style.display = 'block';
            } else {
                dependency.style.display = 'none';
            }

            updateBackupPreview();
            updateSummary();
        }
        
        function updateServiceQuantity(serviceId) {
            const slider = document.getElementById(`${serviceId}-qty`);
            const valueDisplay = document.getElementById(`${serviceId}-value`);
            const qty = parseInt(slider.value);
            valueDisplay.textContent = qty;

            // Update price preview
            updateServicePricePreview(serviceId, qty);
            updateSummary();
        }

        function updateQuantity(serviceId, change) {
            const slider = document.getElementById(`${serviceId}-qty`);
            const currentValue = parseInt(slider.value);
            const minValue = parseInt(slider.min);
            const maxValue = parseInt(slider.max);
            const newValue = currentValue + change;

            if (newValue >= minValue && newValue <= maxValue) {
                slider.value = newValue;
                updateServiceQuantity(serviceId);
            }
        }

        function updateServicePricePreview(serviceId, qty) {
            const preview = document.getElementById(`${serviceId}-preview`);
            if (!preview) return;

            let basePrice, setupPrice, monthlyPrice;

            switch(serviceId) {
                case 'speed-opt':
                    basePrice = 100 * qty;
                    preview.innerHTML = `<strong>Total:</strong> €${basePrice} (€${(basePrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'reopt':
                    basePrice = 50 * qty;
                    preview.innerHTML = `<strong>Total:</strong> €${basePrice} (€${(basePrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'firewall':
                    basePrice = 50 * qty;
                    preview.innerHTML = `<strong>Total:</strong> €${basePrice} (€${(basePrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'hourly-support':
                    basePrice = 100 * qty;
                    preview.innerHTML = `<strong>Total:</strong> €${basePrice} (€${(basePrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'fortress':
                    setupPrice = 50 * qty;
                    monthlyPrice = 3 * qty;
                    preview.innerHTML = `<strong>Setup:</strong> €${setupPrice} (€${(setupPrice * 1.23).toFixed(2)} incl. VAT)<br>
                                        <strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'support247':
                    monthlyPrice = 100 * qty;
                    preview.innerHTML = `<strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
                    break;
                case 'backup':
                    updateBackupPreview();
                    break;
                case 'ext-mon':
                    updateMonitoringPreview();
                    break;
            }
        }

        function updateMonitoringPreview() {
            const qty = parseInt(document.getElementById('ext-mon-qty').value) || 1;
            let features = 0;

            if (document.getElementById('mon-sitemap').checked) features++;
            if (document.getElementById('mon-dom').checked) features++;
            if (document.getElementById('mon-visual').checked) features++;

            const monthlyPrice = 2 * features * qty;
            const preview = document.getElementById('ext-mon-preview');

            if (features === 0) {
                preview.innerHTML = `<strong>Monthly:</strong> €0 (€0 incl. VAT) - Select features above`;
            } else {
                preview.innerHTML = `<strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT) - ${features} feature(s) × ${qty} site(s)`;
            }

            updateSummary();
        }

        function updateBackupPreview() {
            const backupType = document.querySelector('input[name="backup-type"]:checked').value;
            const preview = document.getElementById('backup-preview');

            if (backupType === 'persite') {
                const qty = parseInt(document.getElementById('backup-qty').value) || 1;
                const monthlyPrice = 2 * qty;
                preview.innerHTML = `<strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT)`;
            } else {
                // BYO - calculate based on total RAM
                let totalRAM = 0;
                document.querySelectorAll('.server-item').forEach(server => {
                    const id = server.id.split('-')[1];
                    const ram = parseFloat(document.getElementById(`ram-slider-${id}`).value) || 0;
                    totalRAM += ram;
                });

                const setupPrice = 100;
                const monthlyPrice = 3 * totalRAM;
                preview.innerHTML = `<strong>Setup:</strong> €${setupPrice} (€${(setupPrice * 1.23).toFixed(2)} incl. VAT)<br>
                                    <strong>Monthly:</strong> €${monthlyPrice} (€${(monthlyPrice * 1.23).toFixed(2)} incl. VAT) - ${totalRAM} GB RAM`;
            }
        }
        
        function updateSummary() {
            const t = translations[currentLang];
            let setupSubtotal = 0;
            let monthlySubtotal = 0;
            let setupBreakdown = '';
            let monthlyBreakdown = '';
            
            // Calculate server costs
            let totalRAM = 0;
            let serverTotal = 0;
            let serversWithSetup = 0;
            
            document.querySelectorAll('.server-item').forEach(server => {
                const id = server.id.split('-')[1];
                const ram = parseFloat(document.getElementById(`ram-slider-${id}`).value) || 0;
                const provider = document.getElementById(`provider-${id}`).value;
                const isMigration = document.getElementById(`migration-${id}`).checked;
                
                totalRAM += ram;
                serverTotal++;
                
                // Server management monthly fee
                const mgmtBase = ram * RAM_PRICE;
                monthlySubtotal += mgmtBase;
                
                // Server setup fee (if not migration)
                if (!isMigration) {
                    serversWithSetup++;
                    let setupFee = 50;
                    if (cloudProviders[provider].extraFee > 0) {
                        setupFee += cloudProviders[provider].extraFee;
                    }
                    setupSubtotal += setupFee;
                }
            });
            
            // Add server entries to breakdown
            if (totalRAM > 0) {
                monthlyBreakdown += `
                    <div class="summary-line">
                        <span>${t.serverManagement} (${totalRAM} ${t.gbRam})</span>
                        <span>${formatPrice(totalRAM * RAM_PRICE)}</span>
                    </div>
                `;
                
                if (serversWithSetup > 0) {
                    // Group servers by provider for setup fees
                    const serversByProvider = {};
                    document.querySelectorAll('.server-item').forEach(server => {
                        const id = server.id.split('-')[1];
                        const provider = document.getElementById(`provider-${id}`).value;
                        const isMigration = document.getElementById(`migration-${id}`).checked;
                        if (!isMigration) {
                            if (!serversByProvider[provider]) {
                                serversByProvider[provider] = 0;
                            }
                            serversByProvider[provider]++;
                        }
                    });
                    
                    Object.entries(serversByProvider).forEach(([provider, count]) => {
                        if (count > 0) {
                            const baseSetup = 50 * count;
                            setupBreakdown += `
                                <div class="summary-line">
                                    <span>${t.serverSetup} - ${cloudProviders[provider].name} (${count} servers)</span>
                                    <span>${formatPrice(baseSetup)}</span>
                                </div>
                            `;
                            
                            if (cloudProviders[provider].extraFee > 0) {
                                const extraFee = cloudProviders[provider].extraFee * count;
                                setupBreakdown += `
                                    <div class="summary-line">
                                        <span>${cloudProviders[provider].name} ${t.complexityFee}</span>
                                        <span>${formatPrice(extraFee)}</span>
                                    </div>
                                `;
                            }
                        }
                    });
                }
            }
            
            // Website-specific services
            document.querySelectorAll('.website-item').forEach(website => {
                const websiteId = website.id.split('-')[1];
                const domainInput = document.getElementById(`domain-${websiteId}`);
                const domainName = domainInput ? domainInput.value.trim() : '';
                const displayName = domainName || `Website ${websiteId}`;

                // Speed Optimization per website
                const speedOptCheckbox = document.getElementById(`speed-opt-${websiteId}`);
                if (speedOptCheckbox && speedOptCheckbox.checked) {
                    const priceBase = 100;
                    setupSubtotal += priceBase;
                    setupBreakdown += `
                        <div class="summary-line">
                            <span>Speed Optimization - ${displayName}</span>
                            <span>${formatPrice(priceBase)}</span>
                        </div>
                    `;
                }

                // Re-optimization per website
                const reoptCheckbox = document.getElementById(`reopt-${websiteId}`);
                if (reoptCheckbox && reoptCheckbox.checked) {
                    const priceBase = 50;
                    setupSubtotal += priceBase;
                    setupBreakdown += `
                        <div class="summary-line">
                            <span>Re-optimization - ${displayName}</span>
                            <span>${formatPrice(priceBase)}</span>
                        </div>
                    `;
                }

                // Extended Firewall per website
                const firewallCheckbox = document.getElementById(`firewall-${websiteId}`);
                if (firewallCheckbox && firewallCheckbox.checked) {
                    const priceBase = 50;
                    setupSubtotal += priceBase;
                    setupBreakdown += `
                        <div class="summary-line">
                            <span>Extended Firewall - ${displayName}</span>
                            <span>${formatPrice(priceBase)}</span>
                        </div>
                    `;
                }
            });

            // Legacy global one-time services (if any remain)
            if (document.getElementById('speed-opt') && document.getElementById('speed-opt').checked) {
                const qty = parseInt(document.getElementById('speed-opt-qty').value) || 1;
                const priceBase = 100 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.speedOptName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }

            if (document.getElementById('reopt') && document.getElementById('reopt').checked) {
                const qty = parseInt(document.getElementById('reopt-qty').value) || 1;
                const priceBase = 50 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.reoptName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }

            if (document.getElementById('firewall') && document.getElementById('firewall').checked) {
                const qty = parseInt(document.getElementById('firewall-qty').value) || 1;
                const priceBase = 50 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.firewallName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }
            
            if (document.getElementById('hourly-support').checked) {
                const qty = parseInt(document.getElementById('hourly-support-qty').value) || 1;
                const priceBase = 100 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.hourlyName} (${qty} ${qty === 1 ? t.hour : t.hours})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }
            
            // Server-specific services
            document.querySelectorAll('.server-item').forEach(server => {
                const serverId = server.id.split('-')[1];

                // WP Fortress per server
                const fortressCheckbox = document.getElementById(`fortress-${serverId}`);
                if (fortressCheckbox && fortressCheckbox.checked) {
                    const qty = parseInt(document.getElementById(`fortress-${serverId}-qty`).value) || 1;
                    const setupBase = 50 * qty;
                    const monthlyBase = 3 * qty;

                    setupSubtotal += setupBase;
                    monthlySubtotal += monthlyBase;

                    setupBreakdown += `
                        <div class="summary-line">
                            <span>WP Fortress Setup - Server ${serverId} (${qty} ${t.sites})</span>
                            <span>${formatPrice(setupBase)}</span>
                        </div>
                    `;

                    monthlyBreakdown += `
                        <div class="summary-line">
                            <span>WP Fortress - Server ${serverId} (${qty} ${t.sites})</span>
                            <span>${formatPrice(monthlyBase)}${t.perMonth}</span>
                        </div>
                    `;
                }

                // Extended Monitoring per server
                const extMonCheckbox = document.getElementById(`ext-mon-${serverId}`);
                if (extMonCheckbox && extMonCheckbox.checked) {
                    const qty = parseInt(document.getElementById(`ext-mon-${serverId}-qty`).value) || 1;
                    let features = 0;

                    if (document.getElementById(`mon-sitemap-${serverId}`).checked) features++;
                    if (document.getElementById(`mon-dom-${serverId}`).checked) features++;
                    if (document.getElementById(`mon-visual-${serverId}`).checked) features++;

                    if (features > 0) {
                        const priceBase = 2 * features * qty;
                        monthlySubtotal += priceBase;
                        monthlyBreakdown += `
                            <div class="summary-line">
                                <span>Extended Monitoring - Server ${serverId} (${qty} ${t.sites}, ${features} ${t.features})</span>
                                <span>${formatPrice(priceBase)}${t.perMonth}</span>
                            </div>
                        `;
                    }
                }

                // Backup services per server
                const backupCheckbox = document.getElementById(`backup-${serverId}`);
                if (backupCheckbox && backupCheckbox.checked) {
                    const backupType = document.querySelector(`input[name="backup-type-${serverId}"]:checked`).value;

                    if (backupType === 'persite') {
                        const qty = parseInt(document.getElementById(`backup-${serverId}-qty`).value) || 1;
                        const priceBase = 2 * qty;
                        monthlySubtotal += priceBase;
                        monthlyBreakdown += `
                            <div class="summary-line">
                                <span>Backup - Server ${serverId} (${qty} ${t.sites})</span>
                                <span>${formatPrice(priceBase)}${t.perMonth}</span>
                            </div>
                        `;
                    } else if (backupType === 'byo') {
                        // BYO storage - setup fee + monthly per GB RAM for this server
                        const ram = parseFloat(document.getElementById(`ram-slider-${serverId}`).value) || 0;
                        setupSubtotal += 100;
                        setupBreakdown += `
                            <div class="summary-line">
                                <span>BYO Backup Setup - Server ${serverId}</span>
                                <span>${formatPrice(100)}</span>
                            </div>
                        `;

                        const monthlyBase = 3 * ram;
                        monthlySubtotal += monthlyBase;
                        monthlyBreakdown += `
                            <div class="summary-line">
                                <span>BYO Backup - Server ${serverId} (${ram} ${t.gbRam})</span>
                                <span>${formatPrice(monthlyBase)}${t.perMonth}</span>
                            </div>
                        `;
                    }
                }

                // Server Failover per server
                const failoverCheckbox = document.getElementById(`failover-${serverId}`);
                if (failoverCheckbox && failoverCheckbox.checked) {
                    const setupBase = 50;
                    const monthlyBase = 10;

                    setupSubtotal += setupBase;
                    monthlySubtotal += monthlyBase;

                    setupBreakdown += `
                        <div class="summary-line">
                            <span>Server Failover Setup - Server ${serverId}</span>
                            <span>${formatPrice(setupBase)}</span>
                        </div>
                    `;

                    monthlyBreakdown += `
                        <div class="summary-line">
                            <span>Server Failover - Server ${serverId}</span>
                            <span>${formatPrice(monthlyBase)}${t.perMonth}</span>
                        </div>
                    `;
                }
            });
            
            // 24/7 Support
            if (document.getElementById('support247').checked) {
                const qty = parseInt(document.getElementById('support247-qty').value) || 1;
                const priceBase = 100 * qty;
                monthlySubtotal += priceBase;
                monthlyBreakdown += `
                    <div class="summary-line">
                        <span>${t.support247Name} (${qty} ${t.sites})</span>
                        <span>${formatPrice(priceBase)}${t.perMonth}</span>
                    </div>
                `;
            }
            
            // Apply annual discount to monthly subtotal
            let displayMonthly = monthlySubtotal;
            let annualSavings = 0;
            if (annualBilling && monthlySubtotal > 0) {
                displayMonthly = monthlySubtotal * (1 - ANNUAL_DISCOUNT);
                annualSavings = (monthlySubtotal - displayMonthly) * 12;
            }
            
            // Add VAT if enabled
            let setupTotal = setupSubtotal;
            let monthlyTotal = displayMonthly;
            let setupVAT = 0;
            let monthlyVAT = 0;
            
            if (includeVAT) {
                setupVAT = setupSubtotal * VAT_RATE;
                monthlyVAT = displayMonthly * VAT_RATE;
                setupTotal = setupSubtotal + setupVAT;
                monthlyTotal = displayMonthly + monthlyVAT;
            }
            
            // Always show VAT breakdown in summary
            if (setupSubtotal > 0) {
                setupBreakdown += `
                    <div class="summary-line vat-line">
                        <span>${t.exclVat}</span>
                        <span>${formatPrice(setupSubtotal)}</span>
                    </div>
                    <div class="summary-line vat-line">
                        <span>${t.vatAmount}</span>
                        <span>${formatPrice(setupVAT)}</span>
                    </div>
                `;
            }

            if (displayMonthly > 0) {
                monthlyBreakdown += `
                    <div class="summary-line vat-line">
                        <span>${t.exclVat}</span>
                        <span>${formatPrice(displayMonthly)}</span>
                    </div>
                    <div class="summary-line vat-line">
                        <span>${t.vatAmount}</span>
                        <span>${formatPrice(monthlyVAT)}</span>
                    </div>
                `;
            }
            
            // Update display
            document.getElementById('setup-breakdown').innerHTML = setupBreakdown || '<div class="summary-line"><span>-</span></div>';
            document.getElementById('monthly-breakdown').innerHTML = monthlyBreakdown || '<div class="summary-line"><span>-</span></div>';
            document.getElementById('total-setup').textContent = formatPrice(setupTotal);
            document.getElementById('total-monthly').textContent = formatPrice(monthlyTotal);
            
            // Handle annual billing display
            if (annualBilling) {
                // Show annual totals section
                document.getElementById('annual-totals-section').style.display = 'block';
                
                // Calculate annual values
                const annualSetup = setupTotal; // One-time setup fees (no discount)
                const annualRecurring = monthlyTotal * 12; // Discounted monthly * 12
                const totalAnnualCost = annualSetup + annualRecurring;
                const annualSavingsDisplay = annualSavings; // Already calculated above
                
                // Update annual displays
                document.getElementById('annual-setup').textContent = formatPrice(annualSetup);
                document.getElementById('annual-recurring').textContent = formatPrice(annualRecurring);
                document.getElementById('total-annual-cost').textContent = formatPrice(totalAnnualCost);
                document.getElementById('annual-savings-display').textContent = formatPrice(annualSavingsDisplay);
                
                // Update first period label and value
                document.getElementById('first-month-label').textContent = t.firstYearLabel;
                document.getElementById('first-month-total').textContent = formatPrice(totalAnnualCost);
                
                // Hide the old annual discount notice since we show it in the annual totals section
                document.getElementById('annual-discount').style.display = 'none';
            } else {
                // Hide annual totals section
                document.getElementById('annual-totals-section').style.display = 'none';
                
                // Update first period label and value for monthly
                document.getElementById('first-month-label').textContent = t.firstMonthLabel;
                document.getElementById('first-month-total').textContent = formatPrice(setupTotal + monthlyTotal);
                
                // Show the old annual discount notice if applicable
                if (annualSavings > 0) {
                    document.getElementById('annual-discount').style.display = 'block';
                    document.getElementById('annual-savings').textContent = formatPrice(annualSavings);
                } else {
                    document.getElementById('annual-discount').style.display = 'none';
                }
            }
        }
        
        function generatePDF() {
            const element = document.getElementById('pricing-summary');
            const opt = {
                margin: 1,
                filename: 'server-management-quote.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
            };
            
            // Create a clone for PDF generation
            const clone = element.cloneNode(true);
            clone.style.width = '800px';
            clone.style.position = 'absolute';
            clone.style.left = '-9999px';
            document.body.appendChild(clone);
            
            // Use html2canvas and jsPDF
            html2canvas(clone).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jspdf.jsPDF();
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = canvas.height * imgWidth / canvas.width;
                let heightLeft = imgHeight;
                
                let position = 0;
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }
                
                pdf.save('server-management-quote.pdf');
                document.body.removeChild(clone);
            });
        }
        
        // Initialize dependency visibility and price previews
        function initializeDependencies() {
            // Initialize all price previews for remaining global services
            updateServicePricePreview('speed-opt', 1);
            updateServicePricePreview('reopt', 1);
            updateServicePricePreview('firewall', 1);
            updateServicePricePreview('support247', 1);
        }

        // Initialize
        addServer();
        addWebsite();
        updateAllText();
        updateSummary();
        initializeDependencies();

        // Initialize all service items as disabled
        document.querySelectorAll('.service-item').forEach(item => {
            item.classList.add('disabled');
        });
    </script>
</body>
</html>