<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Management Pricing Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .pricing-notice {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem auto 0;
            max-width: 600px;
            font-size: 1.1rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            gap: 2rem;
            align-items: center;
            justify-content: space-between;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        @media (max-width: 968px) {
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 1rem;
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .included-features {
            background: #e8f5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .included-features h3 {
            color: #2e7d32;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #1b5e20;
            font-size: 0.9rem;
        }
        
        .feature-item::before {
            content: "✓";
            font-weight: bold;
        }
        
        .server-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .server-header h3 {
            color: #495057;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .remove-btn:hover {
            background: #c82333;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 0.25rem;
            color: #666;
            font-size: 0.875rem;
        }
        
        .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            background: white;
        }
        
        .ram-control-group {
            background: #f0f0f0;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .ram-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
        }
        
        .ram-btn {
            background: #667eea;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .ram-btn:hover {
            background: #5a67d8;
        }
        
        .ram-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .ram-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .ram-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .ram-value {
            font-weight: bold;
            color: #667eea;
            min-width: 60px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        .migration-checkbox {
            margin-top: 0.5rem;
        }
        
        .migration-checkbox label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .service-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .service-item.disabled {
            opacity: 0.5;
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .service-header input[type="checkbox"] {
            margin-right: 1rem;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name {
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
        }
        
        .service-price {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .service-controls {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            display: none;
        }
        
        .slider-control {
            margin-bottom: 1rem;
        }
        
        .slider-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .slider-label span {
            color: #666;
            font-size: 0.9rem;
        }
        
        .slider-value {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
        }
        
        .quantity-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .quantity-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .quantity-slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .add-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        
        .add-btn:hover {
            background: #5a67d8;
        }
        
        .pdf-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .pdf-btn:hover {
            background: #218838;
        }
        
        .summary {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            position: sticky;
            top: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .summary h3 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .summary-controls {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .toggle {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ddd;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle.active {
            background: #667eea;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle.active .toggle-slider {
            transform: translateX(26px);
        }
        
        .summary-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .summary-line {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .summary-line:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            padding-top: 1rem;
            border-top: 2px solid #667eea;
        }
        
        .vat-line {
            color: #666;
            font-size: 0.9rem;
            font-style: italic;
        }
        
        .highlight {
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-top: 1rem;
        }
        
        .discount-notice {
            background: #ffc107;
            color: #333;
            padding: 0.75rem;
            border-radius: 8px;
            text-align: center;
            margin-top: 1rem;
            font-weight: 500;
        }
        
        .lang-switch {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .lang-switch:hover {
            background: #667eea;
            color: white;
        }
        
        .lang-switch.active {
            background: #667eea;
            color: white;
        }
        
        .monitoring-features {
            margin-top: 1rem;
            padding: 1rem;
            background: #f0f0f0;
            border-radius: 6px;
        }
        
        .monitoring-feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .monitoring-feature input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
        
        .monitoring-feature label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .backup-options {
            margin-top: 1rem;
        }
        
        .backup-radio {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .backup-radio input[type="radio"] {
            width: 16px;
            height: 16px;
        }
        
        .backup-radio label {
            font-size: 0.9rem;
            color: #666;
            cursor: pointer;
        }
        
        @media print {
            body { background: white; }
            .controls { display: none; }
            .add-btn, .remove-btn, .pdf-btn { display: none; }
            .section { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 id="main-title">Server Management Pricing Calculator</h1>
        <p id="main-subtitle">Professional hosting management services</p>
        <div class="pricing-notice" id="pricing-notice">
            <strong>Base Price:</strong> €6 per GB RAM per month (€7.38 incl. VAT)
        </div>
    </div>
    
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <button class="lang-switch active" onclick="setLanguage('en')">EN</button>
                <button class="lang-switch" onclick="setLanguage('lt')">LT</button>
            </div>
            
            <button class="pdf-btn" onclick="generatePDF()">
                📄 <span id="pdf-btn-text">Save as PDF</span>
            </button>
        </div>
        
        <div class="pricing-grid">
            <div class="left-column">
                <div class="section">
                    <h2>🖥️ <span id="servers-title">Servers</span></h2>
                    
                    <div class="included-features">
                        <h3 id="included-title">Included with every server:</h3>
                        <div class="feature-list">
                            <div class="feature-item" id="feat-1">24/7 critical fault support</div>
                            <div class="feature-item" id="feat-2">Basic server & client cache</div>
                            <div class="feature-item" id="feat-3">7G WAF protection</div>
                            <div class="feature-item" id="feat-4">Basic bot protection</div>
                            <div class="feature-item" id="feat-5">Plugin vulnerability scans</div>
                            <div class="feature-item" id="feat-6">Daily offsite backups</div>
                            <div class="feature-item" id="feat-7">Staging sites</div>
                            <div class="feature-item" id="feat-8">Site cloning</div>
                            <div class="feature-item" id="feat-9">Server monitoring (CPU, RAM, Disk)</div>
                            <div class="feature-item" id="feat-10">Basic uptime monitoring per site</div>
                        </div>
                    </div>
                    
                    <div id="servers-container"></div>
                    <button class="add-btn" onclick="addServer()" id="add-server-btn">+ Add Server</button>
                </div>
                
                <div class="section">
                    <h2>🚀 <span id="oneoff-title">One-Time Services</span></h2>
                    
                    <!-- Speed Optimization -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="speed-opt" onchange="toggleService('speed-opt')">
                            <div class="service-info">
                                <div class="service-name" id="speed-opt-name">Website Speed Optimization</div>
                                <div class="service-price" id="speed-opt-price">€100 per website (€123 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="speed-opt-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="speed-opt-label">Number of websites</span>
                                    <span class="slider-value" id="speed-opt-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="speed-opt-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('speed-opt')">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Re-optimization -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="reopt" onchange="toggleService('reopt')">
                            <div class="service-info">
                                <div class="service-name" id="reopt-name">Re-optimization</div>
                                <div class="service-price" id="reopt-price">€50 per website (€61.50 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="reopt-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="reopt-label">Number of websites</span>
                                    <span class="slider-value" id="reopt-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="reopt-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('reopt')">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Extended Firewall -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="firewall" onchange="toggleService('firewall')">
                            <div class="service-info">
                                <div class="service-name" id="firewall-name">Extended Firewall (Cloudflare)</div>
                                <div class="service-price" id="firewall-price">€50 per website (€61.50 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="firewall-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="firewall-label">Number of websites</span>
                                    <span class="slider-value" id="firewall-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="firewall-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('firewall')">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>📅 <span id="monthly-title">Monthly Services</span></h2>
                    
                    <!-- WP Fortress (linked setup + monthly) -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="fortress" onchange="toggleFortress()">
                            <div class="service-info">
                                <div class="service-name" id="fortress-name">WP Fortress Security</div>
                                <div class="service-price" id="fortress-price">€50 setup + €3/month per site</div>
                            </div>
                        </div>
                        <div class="service-controls" id="fortress-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="fortress-label">Number of websites</span>
                                    <span class="slider-value" id="fortress-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="fortress-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('fortress')">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Extended Monitoring -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="extended-monitoring" onchange="toggleMonitoring()">
                            <div class="service-info">
                                <div class="service-name" id="ext-mon-name">Extended Monitoring</div>
                                <div class="service-price" id="ext-mon-price">€2 per feature per site/month</div>
                            </div>
                        </div>
                        <div class="service-controls" id="ext-mon-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="ext-mon-label">Number of websites</span>
                                    <span class="slider-value" id="ext-mon-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="ext-mon-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('ext-mon')">
                            </div>
                            <div class="monitoring-features">
                                <div class="monitoring-feature">
                                    <input type="checkbox" id="mon-sitemap" onchange="updateSummary()">
                                    <label for="mon-sitemap" id="mon-sitemap-label">Sitemap change monitoring</label>
                                </div>
                                <div class="monitoring-feature">
                                    <input type="checkbox" id="mon-dom" onchange="updateSummary()">
                                    <label for="mon-dom" id="mon-dom-label">Page DOM change monitoring</label>
                                </div>
                                <div class="monitoring-feature">
                                    <input type="checkbox" id="mon-visual" onchange="updateSummary()">
                                    <label for="mon-visual" id="mon-visual-label">Page visual monitoring</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Backup Services -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="backup" onchange="toggleBackup()">
                            <div class="service-info">
                                <div class="service-name" id="backup-name">Hourly Offsite Backup</div>
                                <div class="service-price" id="backup-price">Per site or BYO storage options</div>
                            </div>
                        </div>
                        <div class="service-controls" id="backup-controls">
                            <div class="backup-options">
                                <div class="backup-radio">
                                    <input type="radio" id="backup-persite" name="backup-type" value="persite" checked onchange="updateBackupType()">
                                    <label for="backup-persite" id="backup-persite-label">Per site - €2/month per site</label>
                                </div>
                                <div class="backup-radio">
                                    <input type="radio" id="backup-byo" name="backup-type" value="byo" onchange="updateBackupType()">
                                    <label for="backup-byo" id="backup-byo-label">BYO Storage - €100 setup + €3/month per GB RAM</label>
                                </div>
                            </div>
                            <div class="slider-control" id="backup-site-control">
                                <div class="slider-label">
                                    <span id="backup-site-label">Number of websites</span>
                                    <span class="slider-value" id="backup-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="backup-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('backup')">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 24/7 Support -->
                    <div class="service-item">
                        <div class="service-header">
                            <input type="checkbox" id="support247" onchange="toggleService('support247')">
                            <div class="service-info">
                                <div class="service-name" id="support247-name">24/7 Mission Critical Support</div>
                                <div class="service-price" id="support247-price">€100 per site/month (€123 incl. VAT)</div>
                            </div>
                        </div>
                        <div class="service-controls" id="support247-controls">
                            <div class="slider-control">
                                <div class="slider-label">
                                    <span id="support247-label">Number of websites</span>
                                    <span class="slider-value" id="support247-value">1</span>
                                </div>
                                <input type="range" class="quantity-slider" id="support247-qty" 
                                       min="1" max="20" value="1" oninput="updateServiceQuantity('support247')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="right-column">
                <div class="summary" id="pricing-summary">
                    <h3 id="summary-title">💰 Pricing Summary</h3>
                    
                    <div class="summary-controls">
                        <div class="control-group">
                            <label id="vat-label-summary">Include VAT (23%)</label>
                            <div class="toggle" id="vat-toggle-summary" onclick="toggleVAT()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label id="billing-label-summary">Annual Billing (12% discount)</label>
                            <div class="toggle" id="billing-toggle-summary" onclick="toggleBilling()">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4 id="setup-fees-title">One-Time Setup Fees</h4>
                        <div id="setup-breakdown"></div>
                        <div class="summary-line">
                            <strong id="total-setup-label">Total Setup</strong>
                            <strong id="total-setup">€0</strong>
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4 id="monthly-fees-title">Monthly Fees</h4>
                        <div id="monthly-breakdown"></div>
                        <div class="summary-line">
                            <strong id="total-monthly-label">Total Monthly</strong>
                            <strong id="total-monthly">€0</strong>
                        </div>
                    </div>
                    
                    <div class="highlight">
                        <div id="first-month-label">First Month Total</div>
                        <div style="font-size: 2rem; font-weight: bold;" id="first-month-total">€0</div>
                    </div>
                    
                    <div class="discount-notice" id="annual-discount" style="display: none;">
                        <span id="annual-text">Annual payment saves</span> <span id="annual-savings">€0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        // Translations
        const translations = {
            en: {
                mainTitle: "Server Management Pricing Calculator",
                mainSubtitle: "Professional hosting management services",
                pricingNotice: "Base Price: €6 per GB RAM per month (€7.38 incl. VAT)",
                vatLabel: "Include VAT (23%)",
                billingLabel: "Annual Billing (12% discount)",
                migrationLabel: "Contract start migration (no setup fee)",
                serversTitle: "Servers",
                addServerBtn: "+ Add Server",
                serverLabel: "Server",
                ramLabel: "RAM",
                cloudProviderLabel: "Cloud Provider",
                removeBtn: "Remove",
                oneoffTitle: "One-Time Services",
                monthlyTitle: "Monthly Services",
                summaryTitle: "💰 Pricing Summary",
                setupFeesTitle: "One-Time Setup Fees",
                monthlyFeesTitle: "Monthly Fees",
                totalSetupLabel: "Total Setup",
                totalMonthlyLabel: "Total Monthly",
                firstMonthLabel: "First Month Total",
                annualText: "Annual payment saves",
                pdfBtnText: "Save as PDF",
                includedTitle: "Included with every server:",
                numberOfWebsites: "Number of websites",
                
                // Features
                feat1: "24/7 critical fault support",
                feat2: "Basic server & client cache",
                feat3: "7G WAF protection",
                feat4: "Basic bot protection",
                feat5: "Plugin vulnerability scans",
                feat6: "Daily offsite backups",
                feat7: "Staging sites",
                feat8: "Site cloning",
                feat9: "Server monitoring (CPU, RAM, Disk)",
                feat10: "Basic uptime monitoring per site",
                
                // VAT display
                vatAmount: "VAT (23%)",
                exclVat: "Subtotal (excl. VAT)",
                
                // Services
                speedOptName: "Website Speed Optimization",
                speedOptPrice: "€100 per website (€123 incl. VAT)",
                reoptName: "Re-optimization",
                reoptPrice: "€50 per website (€61.50 incl. VAT)",
                firewallName: "Extended Firewall (Cloudflare)",
                firewallPrice: "€50 per website (€61.50 incl. VAT)",
                fortressName: "WP Fortress Security",
                fortressPrice: "€50 setup + €3/month per site",
                fortressSetupName: "WP Fortress Setup",
                extMonName: "Extended Monitoring",
                extMonPrice: "€2 per feature per site/month",
                monSitemapLabel: "Sitemap change monitoring",
                monDomLabel: "Page DOM change monitoring",
                monVisualLabel: "Page visual monitoring",
                backupName: "Hourly Offsite Backup",
                backupPrice: "Per site or BYO storage options",
                backupPersiteLabel: "Per site - €2/month per site",
                backupByoLabel: "BYO Storage - €100 setup + €3/month per GB RAM",
                backupSetupName: "BYO Storage Backup Setup",
                support247Name: "24/7 Mission Critical Support",
                support247Price: "€100 per site/month (€123 incl. VAT)",
                
                // Summary items
                serverManagement: "Server Management",
                serverSetup: "Server Setup",
                gbRam: "GB RAM",
                websites: "websites",
                sites: "sites",
                perMonth: "/month",
                complexityFee: "Complexity Fee",
                features: "features"
            },
            lt: {
                mainTitle: "Serverių Valdymo Kainų Skaičiuoklė",
                mainSubtitle: "Profesionalios hostingo valdymo paslaugos",
                pricingNotice: "Bazinė kaina: €6 už GB RAM per mėnesį (€7.38 su PVM)",
                vatLabel: "Įtraukti PVM (23%)",
                billingLabel: "Metinis mokėjimas (12% nuolaida)",
                migrationLabel: "Sutarties pradžios migracija (be diegimo mokesčio)",
                serversTitle: "Serveriai",
                addServerBtn: "+ Pridėti serverį",
                serverLabel: "Serveris",
                ramLabel: "RAM",
                cloudProviderLabel: "Debesų tiekėjas",
                removeBtn: "Pašalinti",
                oneoffTitle: "Vienkartinės paslaugos",
                monthlyTitle: "Mėnesinės paslaugos",
                summaryTitle: "💰 Kainų suvestinė",
                setupFeesTitle: "Vienkartiniai mokesčiai",
                monthlyFeesTitle: "Mėnesiniai mokesčiai",
                totalSetupLabel: "Viso vienkartiniai",
                totalMonthlyLabel: "Viso per mėnesį",
                firstMonthLabel: "Pirmo mėnesio suma",
                annualText: "Metinis mokėjimas sutaupo",
                pdfBtnText: "Išsaugoti PDF",
                includedTitle: "Įtraukta su kiekvienu serveriu:",
                numberOfWebsites: "Svetainių skaičius",
                
                // Features
                feat1: "24/7 kritinių gedimų palaikymas",
                feat2: "Bazinis serverio ir kliento talpykla",
                feat3: "7G WAF apsauga",
                feat4: "Bazinė botų apsauga",
                feat5: "Įskiepių pažeidžiamumo nuskaitymai",
                feat6: "Kasdieninės atsarginės kopijos",
                feat7: "Bandymo svetainės",
                feat8: "Svetainių klonavimas",
                feat9: "Serverio stebėjimas (CPU, RAM, Diskas)",
                feat10: "Bazinis veikimo laiko stebėjimas",
                
                // VAT display
                vatAmount: "PVM (23%)",
                exclVat: "Tarpinė suma (be PVM)",
                
                // Services
                speedOptName: "Svetainės greičio optimizavimas",
                speedOptPrice: "€100 už svetainę (€123 su PVM)",
                reoptName: "Pakartotinis optimizavimas",
                reoptPrice: "€50 už svetainę (€61.50 su PVM)",
                firewallName: "Išplėstinė ugniasienė (Cloudflare)",
                firewallPrice: "€50 už svetainę (€61.50 su PVM)",
                fortressName: "WP Fortress apsauga",
                fortressPrice: "€50 diegimas + €3/mėn už svetainę",
                fortressSetupName: "WP Fortress diegimas",
                extMonName: "Išplėstinis stebėjimas",
                extMonPrice: "€2 už funkciją už svetainę/mėn",
                monSitemapLabel: "Svetainės žemėlapio pokyčių stebėjimas",
                monDomLabel: "Puslapio DOM pokyčių stebėjimas",
                monVisualLabel: "Puslapio vizualinis stebėjimas",
                backupName: "Valandinės atsarginės kopijos",
                backupPrice: "Už svetainę arba BYO saugyklos parinktys",
                backupPersiteLabel: "Už svetainę - €2/mėn už svetainę",
                backupByoLabel: "BYO saugykla - €100 diegimas + €3/mėn už GB RAM",
                backupSetupName: "BYO saugyklos atsarginių kopijų diegimas",
                support247Name: "24/7 kritinių situacijų palaikymas",
                support247Price: "€100 už svetainę/mėn (€123 su PVM)",
                
                // Summary items
                serverManagement: "Serverių valdymas",
                serverSetup: "Serverio diegimas",
                gbRam: "GB RAM",
                websites: "svetainės",
                sites: "svetainės",
                perMonth: "/mėn",
                complexityFee: "Sudėtingumo mokestis",
                features: "funkcijos"
            }
        };
        
        let currentLang = 'en';
        let includeVAT = false;
        let annualBilling = false;
        let serverCount = 0;
        
        const VAT_RATE = 0.23;
        const ANNUAL_DISCOUNT = 0.12;
        const RAM_PRICE = 6; // €6 per GB RAM
        
        const cloudProviders = {
            'vultr': { name: 'Vultr', extraFee: 0 },
            'digitalocean': { name: 'Digital Ocean', extraFee: 0 },
            'upcloud': { name: 'UpCloud', extraFee: 0 },
            'hetzner': { name: 'Hetzner', extraFee: 0 },
            'linode': { name: 'Linode', extraFee: 0 },
            'aws': { name: 'AWS', extraFee: 50 },
            'gcloud': { name: 'Google Cloud', extraFee: 50 }
        };
        
        function setLanguage(lang) {
            currentLang = lang;
            document.querySelectorAll('.lang-switch').forEach(btn => {
                btn.classList.toggle('active', btn.textContent.toLowerCase() === lang);
            });
            updateAllText();
            updateSummary();
        }
        
        function formatPrice(amount) {
            return `€${amount.toFixed(2)}`;
        }
        
        function updateAllText() {
            const t = translations[currentLang];
            
            // Update static text
            document.getElementById('main-title').textContent = t.mainTitle;
            document.getElementById('main-subtitle').textContent = t.mainSubtitle;
            document.getElementById('pricing-notice').innerHTML = `<strong>${t.pricingNotice.split(':')[0]}:</strong> ${t.pricingNotice.split(':')[1]}`;
            document.getElementById('vat-label-summary').textContent = t.vatLabel;
            document.getElementById('billing-label-summary').textContent = t.billingLabel;
            document.getElementById('servers-title').textContent = t.serversTitle;
            document.getElementById('add-server-btn').textContent = t.addServerBtn;
            document.getElementById('oneoff-title').textContent = t.oneoffTitle;
            document.getElementById('monthly-title').textContent = t.monthlyTitle;
            document.getElementById('summary-title').textContent = t.summaryTitle;
            document.getElementById('setup-fees-title').textContent = t.setupFeesTitle;
            document.getElementById('monthly-fees-title').textContent = t.monthlyFeesTitle;
            document.getElementById('total-setup-label').textContent = t.totalSetupLabel;
            document.getElementById('total-monthly-label').textContent = t.totalMonthlyLabel;
            document.getElementById('first-month-label').textContent = t.firstMonthLabel;
            document.getElementById('annual-text').textContent = t.annualText;
            document.getElementById('pdf-btn-text').textContent = t.pdfBtnText;
            document.getElementById('included-title').textContent = t.includedTitle;
            
            // Update features
            for (let i = 1; i <= 10; i++) {
                document.getElementById(`feat-${i}`).textContent = t[`feat${i}`];
            }
            
            // Update service names and prices
            document.getElementById('speed-opt-name').textContent = t.speedOptName;
            document.getElementById('speed-opt-price').textContent = t.speedOptPrice;
            document.getElementById('speed-opt-label').textContent = t.numberOfWebsites;
            
            document.getElementById('reopt-name').textContent = t.reoptName;
            document.getElementById('reopt-price').textContent = t.reoptPrice;
            document.getElementById('reopt-label').textContent = t.numberOfWebsites;
            
            document.getElementById('firewall-name').textContent = t.firewallName;
            document.getElementById('firewall-price').textContent = t.firewallPrice;
            document.getElementById('firewall-label').textContent = t.numberOfWebsites;
            
            document.getElementById('fortress-name').textContent = t.fortressName;
            document.getElementById('fortress-price').textContent = t.fortressPrice;
            document.getElementById('fortress-label').textContent = t.numberOfWebsites;
            
            document.getElementById('ext-mon-name').textContent = t.extMonName;
            document.getElementById('ext-mon-price').textContent = t.extMonPrice;
            document.getElementById('ext-mon-label').textContent = t.numberOfWebsites;
            document.getElementById('mon-sitemap-label').textContent = t.monSitemapLabel;
            document.getElementById('mon-dom-label').textContent = t.monDomLabel;
            document.getElementById('mon-visual-label').textContent = t.monVisualLabel;
            
            document.getElementById('backup-name').textContent = t.backupName;
            document.getElementById('backup-price').textContent = t.backupPrice;
            document.getElementById('backup-persite-label').textContent = t.backupPersiteLabel;
            document.getElementById('backup-byo-label').textContent = t.backupByoLabel;
            document.getElementById('backup-site-label').textContent = t.numberOfWebsites;
            
            document.getElementById('support247-name').textContent = t.support247Name;
            document.getElementById('support247-price').textContent = t.support247Price;
            document.getElementById('support247-label').textContent = t.numberOfWebsites;
            
            // Update server labels
            document.querySelectorAll('.server-item').forEach((server, index) => {
                server.querySelector('h3').textContent = `${t.serverLabel} ${index + 1}`;
                server.querySelector('.ram-label').textContent = t.ramLabel;
                server.querySelector('.provider-label').textContent = t.cloudProviderLabel;
                server.querySelector('.migration-label').textContent = t.migrationLabel;
                const removeBtn = server.querySelector('.remove-btn');
                if (removeBtn) removeBtn.textContent = t.removeBtn;
            });
        }
        
        function toggleVAT() {
            includeVAT = !includeVAT;
            document.getElementById('vat-toggle-summary').classList.toggle('active', includeVAT);
            updateSummary();
        }
        
        function toggleBilling() {
            annualBilling = !annualBilling;
            document.getElementById('billing-toggle-summary').classList.toggle('active', annualBilling);
            document.getElementById('annual-discount').style.display = annualBilling ? 'block' : 'none';
            updateSummary();
        }
        
        function addServer() {
            serverCount++;
            const container = document.getElementById('servers-container');
            const serverDiv = document.createElement('div');
            serverDiv.className = 'server-item';
            serverDiv.id = `server-${serverCount}`;
            
            const t = translations[currentLang];
            
            serverDiv.innerHTML = `
                <div class="server-header">
                    <h3>${t.serverLabel} ${serverCount}</h3>
                    ${serverCount > 1 ? `<button class="remove-btn" onclick="removeServer(${serverCount})">${t.removeBtn}</button>` : ''}
                </div>
                <div class="input-group">
                    <label class="provider-label">${t.cloudProviderLabel}</label>
                    <select id="provider-${serverCount}" onchange="updateSummary()">
                        ${Object.entries(cloudProviders).map(([key, provider]) => 
                            `<option value="${key}">${provider.name}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="ram-control-group">
                    <label class="ram-label">${t.ramLabel}</label>
                    <div class="ram-controls">
                        <button class="ram-btn" onclick="updateRAM(${serverCount}, -1)">-</button>
                        <input type="range" class="ram-slider" id="ram-slider-${serverCount}" 
                               min="1" max="64" value="4" oninput="updateRAMDisplay(${serverCount})">
                        <button class="ram-btn" onclick="updateRAM(${serverCount}, 1)">+</button>
                        <div class="ram-value" id="ram-value-${serverCount}">4 GB</div>
                    </div>
                </div>
                <div class="migration-checkbox">
                    <label>
                        <input type="checkbox" id="migration-${serverCount}" onchange="updateSummary()">
                        <span class="migration-label">${t.migrationLabel}</span>
                    </label>
                </div>
            `;
            
            container.appendChild(serverDiv);
            updateSummary();
        }
        
        function updateRAM(serverId, change) {
            const slider = document.getElementById(`ram-slider-${serverId}`);
            const newValue = parseInt(slider.value) + change;
            if (newValue >= 1 && newValue <= 64) {
                slider.value = newValue;
                updateRAMDisplay(serverId);
            }
        }
        
        function updateRAMDisplay(serverId) {
            const slider = document.getElementById(`ram-slider-${serverId}`);
            const value = document.getElementById(`ram-value-${serverId}`);
            value.textContent = `${slider.value} GB`;
            updateSummary();
        }
        
        function removeServer(id) {
            document.getElementById(`server-${id}`).remove();
            updateSummary();
        }
        
        function toggleService(serviceId) {
            const checked = document.getElementById(serviceId).checked;
            const controls = document.getElementById(`${serviceId}-controls`);
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById(serviceId).closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleFortress() {
            const checked = document.getElementById('fortress').checked;
            const controls = document.getElementById('fortress-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById('fortress').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleMonitoring() {
            const checked = document.getElementById('extended-monitoring').checked;
            const controls = document.getElementById('ext-mon-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            if (!checked) {
                document.getElementById('mon-sitemap').checked = false;
                document.getElementById('mon-dom').checked = false;
                document.getElementById('mon-visual').checked = false;
            }
            
            // Update disabled state
            const serviceItem = document.getElementById('extended-monitoring').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function toggleBackup() {
            const checked = document.getElementById('backup').checked;
            const controls = document.getElementById('backup-controls');
            controls.style.display = checked ? 'block' : 'none';
            
            // Update disabled state
            const serviceItem = document.getElementById('backup').closest('.service-item');
            if (checked) {
                serviceItem.classList.remove('disabled');
            } else {
                serviceItem.classList.add('disabled');
            }
            
            updateSummary();
        }
        
        function updateBackupType() {
            const backupType = document.querySelector('input[name="backup-type"]:checked').value;
            const siteControl = document.getElementById('backup-site-control');
            siteControl.style.display = backupType === 'persite' ? 'block' : 'none';
            updateSummary();
        }
        
        function updateServiceQuantity(serviceId) {
            const slider = document.getElementById(`${serviceId}-qty`);
            const valueDisplay = document.getElementById(`${serviceId}-value`);
            valueDisplay.textContent = slider.value;
            updateSummary();
        }
        
        function updateSummary() {
            const t = translations[currentLang];
            let setupSubtotal = 0;
            let monthlySubtotal = 0;
            let setupBreakdown = '';
            let monthlyBreakdown = '';
            
            // Calculate server costs
            let totalRAM = 0;
            let serverTotal = 0;
            let serversWithSetup = 0;
            
            document.querySelectorAll('.server-item').forEach(server => {
                const id = server.id.split('-')[1];
                const ram = parseFloat(document.getElementById(`ram-slider-${id}`).value) || 0;
                const provider = document.getElementById(`provider-${id}`).value;
                const isMigration = document.getElementById(`migration-${id}`).checked;
                
                totalRAM += ram;
                serverTotal++;
                
                // Server management monthly fee
                const mgmtBase = ram * RAM_PRICE;
                monthlySubtotal += mgmtBase;
                
                // Server setup fee (if not migration)
                if (!isMigration) {
                    serversWithSetup++;
                    let setupFee = 50;
                    if (cloudProviders[provider].extraFee > 0) {
                        setupFee += cloudProviders[provider].extraFee;
                    }
                    setupSubtotal += setupFee;
                }
            });
            
            // Add server entries to breakdown
            if (totalRAM > 0) {
                monthlyBreakdown += `
                    <div class="summary-line">
                        <span>${t.serverManagement} (${totalRAM} ${t.gbRam})</span>
                        <span>${formatPrice(totalRAM * RAM_PRICE)}</span>
                    </div>
                `;
                
                if (serversWithSetup > 0) {
                    // Group servers by provider for setup fees
                    const serversByProvider = {};
                    document.querySelectorAll('.server-item').forEach(server => {
                        const id = server.id.split('-')[1];
                        const provider = document.getElementById(`provider-${id}`).value;
                        const isMigration = document.getElementById(`migration-${id}`).checked;
                        if (!isMigration) {
                            if (!serversByProvider[provider]) {
                                serversByProvider[provider] = 0;
                            }
                            serversByProvider[provider]++;
                        }
                    });
                    
                    Object.entries(serversByProvider).forEach(([provider, count]) => {
                        if (count > 0) {
                            const baseSetup = 50 * count;
                            setupBreakdown += `
                                <div class="summary-line">
                                    <span>${t.serverSetup} - ${cloudProviders[provider].name} (${count} servers)</span>
                                    <span>${formatPrice(baseSetup)}</span>
                                </div>
                            `;
                            
                            if (cloudProviders[provider].extraFee > 0) {
                                const extraFee = cloudProviders[provider].extraFee * count;
                                setupBreakdown += `
                                    <div class="summary-line">
                                        <span>${cloudProviders[provider].name} ${t.complexityFee}</span>
                                        <span>${formatPrice(extraFee)}</span>
                                    </div>
                                `;
                            }
                        }
                    });
                }
            }
            
            // One-time services
            if (document.getElementById('speed-opt').checked) {
                const qty = parseInt(document.getElementById('speed-opt-qty').value) || 1;
                const priceBase = 100 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.speedOptName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }
            
            if (document.getElementById('reopt').checked) {
                const qty = parseInt(document.getElementById('reopt-qty').value) || 1;
                const priceBase = 50 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.reoptName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }
            
            if (document.getElementById('firewall').checked) {
                const qty = parseInt(document.getElementById('firewall-qty').value) || 1;
                const priceBase = 50 * qty;
                setupSubtotal += priceBase;
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.firewallName} (${qty} ${t.websites})</span>
                        <span>${formatPrice(priceBase)}</span>
                    </div>
                `;
            }
            
            // WP Fortress (linked setup + monthly)
            if (document.getElementById('fortress').checked) {
                const qty = parseInt(document.getElementById('fortress-qty').value) || 1;
                const setupBase = 50 * qty;
                const monthlyBase = 3 * qty;
                
                setupSubtotal += setupBase;
                monthlySubtotal += monthlyBase;
                
                setupBreakdown += `
                    <div class="summary-line">
                        <span>${t.fortressSetupName} (${qty} ${t.sites})</span>
                        <span>${formatPrice(setupBase)}</span>
                    </div>
                `;
                
                monthlyBreakdown += `
                    <div class="summary-line">
                        <span>${t.fortressName} (${qty} ${t.sites})</span>
                        <span>${formatPrice(monthlyBase)}${t.perMonth}</span>
                    </div>
                `;
            }
            
            // Extended Monitoring
            if (document.getElementById('extended-monitoring').checked) {
                const qty = parseInt(document.getElementById('ext-mon-qty').value) || 1;
                let features = 0;
                let featureNames = [];
                
                if (document.getElementById('mon-sitemap').checked) {
                    features++;
                    featureNames.push(t.monSitemapLabel);
                }
                if (document.getElementById('mon-dom').checked) {
                    features++;
                    featureNames.push(t.monDomLabel);
                }
                if (document.getElementById('mon-visual').checked) {
                    features++;
                    featureNames.push(t.monVisualLabel);
                }
                
                if (features > 0) {
                    const priceBase = 2 * features * qty;
                    monthlySubtotal += priceBase;
                    monthlyBreakdown += `
                        <div class="summary-line">
                            <span>${t.extMonName} (${qty} ${t.sites}, ${features} ${t.features})</span>
                            <span>${formatPrice(priceBase)}${t.perMonth}</span>
                        </div>
                    `;
                }
            }
            
            // Backup services
            if (document.getElementById('backup').checked) {
                const backupType = document.querySelector('input[name="backup-type"]:checked').value;
                
                if (backupType === 'persite') {
                    const qty = parseInt(document.getElementById('backup-qty').value) || 1;
                    const priceBase = 2 * qty;
                    monthlySubtotal += priceBase;
                    monthlyBreakdown += `
                        <div class="summary-line">
                            <span>${t.backupName} (${qty} ${t.sites})</span>
                            <span>${formatPrice(priceBase)}${t.perMonth}</span>
                        </div>
                    `;
                } else if (backupType === 'byo' && totalRAM > 0) {
                    // BYO storage - setup fee + monthly per GB RAM
                    setupSubtotal += 100;
                    setupBreakdown += `
                        <div class="summary-line">
                            <span>${t.backupSetupName}</span>
                            <span>${formatPrice(100)}</span>
                        </div>
                    `;
                    
                    const monthlyBase = 3 * totalRAM;
                    monthlySubtotal += monthlyBase;
                    monthlyBreakdown += `
                        <div class="summary-line">
                            <span>${t.backupName} - BYO (${totalRAM} ${t.gbRam})</span>
                            <span>${formatPrice(monthlyBase)}${t.perMonth}</span>
                        </div>
                    `;
                }
            }
            
            // 24/7 Support
            if (document.getElementById('support247').checked) {
                const qty = parseInt(document.getElementById('support247-qty').value) || 1;
                const priceBase = 100 * qty;
                monthlySubtotal += priceBase;
                monthlyBreakdown += `
                    <div class="summary-line">
                        <span>${t.support247Name} (${qty} ${t.sites})</span>
                        <span>${formatPrice(priceBase)}${t.perMonth}</span>
                    </div>
                `;
            }
            
            // Apply annual discount to monthly subtotal
            let displayMonthly = monthlySubtotal;
            let annualSavings = 0;
            if (annualBilling && monthlySubtotal > 0) {
                displayMonthly = monthlySubtotal * (1 - ANNUAL_DISCOUNT);
                annualSavings = (monthlySubtotal - displayMonthly) * 12;
            }
            
            // Add VAT if enabled
            let setupTotal = setupSubtotal;
            let monthlyTotal = displayMonthly;
            let setupVAT = 0;
            let monthlyVAT = 0;
            
            if (includeVAT) {
                setupVAT = setupSubtotal * VAT_RATE;
                monthlyVAT = displayMonthly * VAT_RATE;
                setupTotal = setupSubtotal + setupVAT;
                monthlyTotal = displayMonthly + monthlyVAT;
            }
            
            // Always show VAT breakdown in summary
            if (setupSubtotal > 0) {
                setupBreakdown += `
                    <div class="summary-line vat-line">
                        <span>${t.exclVat}</span>
                        <span>${formatPrice(setupSubtotal)}</span>
                    </div>
                    <div class="summary-line vat-line">
                        <span>${t.vatAmount}</span>
                        <span>${formatPrice(setupVAT)}</span>
                    </div>
                `;
            }
            
            if (monthlySubtotal > 0) {
                monthlyBreakdown += `
                    <div class="summary-line vat-line">
                        <span>${t.exclVat}</span>
                        <span>${formatPrice(displayMonthly)}</span>
                    </div>
                    <div class="summary-line vat-line">
                        <span>${t.vatAmount}</span>
                        <span>${formatPrice(monthlyVAT)}</span>
                    </div>
                `;
            }
            
            // Update display
            document.getElementById('setup-breakdown').innerHTML = setupBreakdown || '<div class="summary-line"><span>-</span></div>';
            document.getElementById('monthly-breakdown').innerHTML = monthlyBreakdown || '<div class="summary-line"><span>-</span></div>';
            document.getElementById('total-setup').textContent = formatPrice(setupTotal);
            document.getElementById('total-monthly').textContent = formatPrice(monthlyTotal);
            document.getElementById('first-month-total').textContent = formatPrice(setupTotal + monthlyTotal);
            
            if (annualBilling && annualSavings > 0) {
                document.getElementById('annual-savings').textContent = formatPrice(annualSavings);
            }
        }
        
        function generatePDF() {
            const element = document.getElementById('pricing-summary');
            const opt = {
                margin: 1,
                filename: 'server-management-quote.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
            };
            
            // Create a clone for PDF generation
            const clone = element.cloneNode(true);
            clone.style.width = '800px';
            clone.style.position = 'absolute';
            clone.style.left = '-9999px';
            document.body.appendChild(clone);
            
            // Use html2canvas and jsPDF
            html2canvas(clone).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jspdf.jsPDF();
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = canvas.height * imgWidth / canvas.width;
                let heightLeft = imgHeight;
                
                let position = 0;
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }
                
                pdf.save('server-management-quote.pdf');
                document.body.removeChild(clone);
            });
        }
        
        // Initialize
        addServer();
        updateAllText();
        updateSummary();
        
        // Initialize all service items as disabled
        document.querySelectorAll('.service-item').forEach(item => {
            item.classList.add('disabled');
        });
    </script>
</body>
</html>