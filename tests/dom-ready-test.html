<!DOCTYPE html>
<html>
<head>
    <title>DOM Ready Test</title>
</head>
<body>
    <h1>DOM Ready Test</h1>
    <div id="test-container"></div>
    <button onclick="testAddition()">Test Add</button>
    
    <script>
        console.log('Script tag parsed');
        
        function testAddition() {
            console.log('testAddition called');
            const container = document.getElementById('test-container');
            if (container) {
                const div = document.createElement('div');
                div.textContent = 'Item added at ' + new Date().toLocaleTimeString();
                container.appendChild(div);
                console.log('Item added successfully');
            } else {
                console.error('Container not found');
            }
        }
        
        // Test immediate execution
        console.log('Testing immediate execution...');
        testAddition();
        
        // Test on DOMContentLoaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded fired');
            testAddition();
        });
        
        // Test on window load
        window.addEventListener('load', function() {
            console.log('Window load fired');
            testAddition();
        });
        
        console.log('Script end');
    </script>
</body>
</html>