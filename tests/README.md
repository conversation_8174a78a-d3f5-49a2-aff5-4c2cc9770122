# BSBD Calculator Test Files

This directory contains various test files and debugging utilities used during development and troubleshooting of the BSBD Calculator.

## Test Files

### Core Functionality Tests
- **`functionality-test.html`** - Complete functionality test suite for all calculator features
- **`simple-test.html`** - Basic test for core calculator operations
- **`test-fixes.html`** - Test file for verifying JavaScript syntax fixes

### Debugging Tools
- **`debug-test.html`** - General debugging tool with console logging
- **`debug-initialization.html`** - Specific test for initialization sequence debugging
- **`dom-ready-test.html`** - Tests DOM readiness and element availability
- **`test-console-errors.html`** - Console error detection and reporting

### PDF Generation
- **`test-pdf.html`** - PDF generation functionality test
- **`validate-pdf.js`** - PDF validation utility script

## Usage

These test files are designed for development and debugging purposes. They help ensure:

1. **JavaScript Functionality** - All interactive elements work correctly
2. **Syntax Validation** - No parsing errors or syntax issues
3. **DOM Manipulation** - Proper element creation and manipulation
4. **PDF Export** - Quote generation and export features
5. **Error Handling** - Graceful handling of edge cases

## Running Tests

To run any test:
1. Open the desired HTML test file in a web browser
2. Open Developer Tools (F12) to view console output
3. Follow any on-screen instructions or test prompts
4. Check console for detailed logging and error reports

## Maintenance

These test files should be updated when:
- New features are added to the main calculator
- Bug fixes are implemented
- JavaScript structure changes
- New validation requirements are introduced