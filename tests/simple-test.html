<!DOCTYPE html>
<html>
<head>
    <title>Simple Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; font-size: 16px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>BSBD Calculator Button Test</h1>
    
    <div>
        <button onclick="testAddServerFunction()">Test Add Server Function</button>
        <button onclick="testAddWebsiteFunction()">Test Add Website Function</button>
        <button onclick="testInitialization()">Test Initialization</button>
        <button onclick="checkElements()">Check HTML Elements</button>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function testAddServerFunction() {
            try {
                // Load the main HTML file content to test functions
                fetch('./index.html')
                    .then(response => response.text())  
                    .then(html => {
                        // Extract JavaScript from the HTML
                        const scriptMatch = html.match(/<script>([\s\S]*?)<\/script>/);
                        if (scriptMatch) {
                            const jsCode = scriptMatch[1];
                            
                            // Check if addServer function exists in the code
                            const hasAddServer = jsCode.includes('function addServer()');
                            const hasCloudProviders = jsCode.includes('const cloudProviders');
                            const hasTranslations = jsCode.includes('const translations');
                            
                            log(`addServer function found: ${hasAddServer}`, hasAddServer ? 'success' : 'error');
                            log(`cloudProviders defined: ${hasCloudProviders}`, hasCloudProviders ? 'success' : 'error');
                            log(`translations defined: ${hasTranslations}`, hasTranslations ? 'success' : 'error');
                            
                            // Check for potential syntax issues
                            const openBraces = (jsCode.match(/{/g) || []).length;
                            const closeBraces = (jsCode.match(/}/g) || []).length;
                            log(`Brace balance check: ${openBraces} open, ${closeBraces} close`, 
                                openBraces === closeBraces ? 'success' : 'error');
                        } else {
                            log('No script tag found in HTML', 'error');
                        }
                    })
                    .catch(error => {
                        log(`Error loading HTML: ${error.message}`, 'error');
                    });
            } catch (error) {
                log(`Test error: ${error.message}`, 'error');
            }
        }

        function testAddWebsiteFunction() {
            fetch('./index.html')
                .then(response => response.text())
                .then(html => {
                    const scriptMatch = html.match(/<script>([\s\S]*?)<\/script>/);
                    if (scriptMatch) {
                        const jsCode = scriptMatch[1];
                        const hasAddWebsite = jsCode.includes('function addWebsite()');
                        const websiteInitCall = jsCode.includes('addWebsite();');
                        
                        log(`addWebsite function found: ${hasAddWebsite}`, hasAddWebsite ? 'success' : 'error');
                        log(`addWebsite() initialization call found: ${websiteInitCall}`, websiteInitCall ? 'success' : 'error');
                    }
                })
                .catch(error => log(`Error: ${error.message}`, 'error'));
        }

        function testInitialization() {  
            fetch('./index.html')
                .then(response => response.text())
                .then(html => {
                    const scriptMatch = html.match(/<script>([\s\S]*?)<\/script>/);
                    if (scriptMatch) {
                        const jsCode = scriptMatch[1];
                        
                        // Check for initialization calls at the end
                        const hasAddServerInit = jsCode.includes('addServer();');
                        const hasAddWebsiteInit = jsCode.includes('addWebsite();');
                        const hasUpdateAllText = jsCode.includes('updateAllText();');
                        const hasUpdateSummary = jsCode.includes('updateSummary();');
                        
                        log(`addServer() init call: ${hasAddServerInit}`, hasAddServerInit ? 'success' : 'error');
                        log(`addWebsite() init call: ${hasAddWebsiteInit}`, hasAddWebsiteInit ? 'success' : 'error');
                        log(`updateAllText() init call: ${hasUpdateAllText}`, hasUpdateAllText ? 'success' : 'error');
                        log(`updateSummary() init call: ${hasUpdateSummary}`, hasUpdateSummary ? 'success' : 'error');
                    }
                })
                .catch(error => log(`Error: ${error.message}`, 'error'));
        }

        function checkElements() {
            fetch('./index.html')
                .then(response => response.text())
                .then(html => {
                    const hasServersContainer = html.includes('id="servers-container"');
                    const hasWebsitesContainer = html.includes('id="websites-container"');
                    const hasAddServerBtn = html.includes('id="add-server-btn"');
                    const hasAddWebsiteBtn = html.includes('id="add-website-btn"');
                    
                    log(`servers-container element: ${hasServersContainer}`, hasServersContainer ? 'success' : 'error');
                    log(`websites-container element: ${hasWebsitesContainer}`, hasWebsitesContainer ? 'success' : 'error');
                    log(`add-server-btn element: ${hasAddServerBtn}`, hasAddServerBtn ? 'success' : 'error');
                    log(`add-website-btn element: ${hasAddWebsiteBtn}`, hasAddWebsiteBtn ? 'success' : 'error');
                    
                    // Check for onclick handlers
                    const hasAddServerOnClick = html.includes('onclick="addServer()"');
                    const hasAddWebsiteOnClick = html.includes('onclick="addWebsite()"');
                    
                    log(`Add Server onclick handler: ${hasAddServerOnClick}`, hasAddServerOnClick ? 'success' : 'error');
                    log(`Add Website onclick handler: ${hasAddWebsiteOnClick}`, hasAddWebsiteOnClick ? 'success' : 'error');
                })
                .catch(error => log(`Error: ${error.message}`, 'error'));
        }

        // Auto-run basic checks on load
        window.onload = () => {
            log('Starting automatic checks...', 'info');
            setTimeout(checkElements, 100);
            setTimeout(testInitialization, 200);
        };
    </script>
</body>
</html>