// PDF Generation Validation Script
// This script can be run in the browser console to test PDF functionality

function validatePDFGeneration() {
    console.log('🔍 Validating PDF Generation Functionality...');
    
    // Test 1: Check if required libraries are loaded
    console.log('\n📚 Testing Library Dependencies:');
    
    if (typeof jspdf !== 'undefined' && jspdf.jsPDF) {
        console.log('✅ jsPDF library loaded successfully');
    } else {
        console.error('❌ jsPDF library not found');
        return false;
    }
    
    if (typeof html2canvas !== 'undefined') {
        console.log('✅ html2canvas library loaded successfully');
    } else {
        console.error('❌ html2canvas library not found');
        return false;
    }
    
    // Test 2: Check if generatePDF function exists
    console.log('\n🔧 Testing PDF Generation Function:');
    
    if (typeof generatePDF === 'function') {
        console.log('✅ generatePDF function exists');
    } else {
        console.error('❌ generatePDF function not found');
        return false;
    }
    
    // Test 3: Check if createPDFContent function exists
    if (typeof createPDFContent === 'function') {
        console.log('✅ createPDFContent function exists');
    } else {
        console.error('❌ createPDFContent function not found');
        return false;
    }
    
    // Test 4: Validate timestamp generation
    console.log('\n⏰ Testing Timestamp Generation:');
    
    try {
        const now = new Date();
        const timestamp = now.getFullYear() + '-' + 
                        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(now.getDate()).padStart(2, '0') + '-' + 
                        String(now.getHours()).padStart(2, '0') + 
                        String(now.getMinutes()).padStart(2, '0') + 
                        String(now.getSeconds()).padStart(2, '0');
        const filename = `server-management-quote-${timestamp}.pdf`;
        
        console.log(`✅ Timestamp generated: ${timestamp}`);
        console.log(`✅ Filename generated: ${filename}`);
        
        // Validate format
        const timestampRegex = /^\d{4}-\d{2}-\d{2}-\d{6}$/;
        if (timestampRegex.test(timestamp)) {
            console.log('✅ Timestamp format is correct');
        } else {
            console.error('❌ Timestamp format is incorrect');
            return false;
        }
    } catch (error) {
        console.error('❌ Error generating timestamp:', error);
        return false;
    }
    
    // Test 5: Check if required DOM elements exist
    console.log('\n🏗️ Testing DOM Elements:');
    
    const requiredElements = [
        'pricing-summary',
        'setup-breakdown',
        'monthly-breakdown',
        'total-setup',
        'total-monthly',
        'first-month-total'
    ];
    
    let missingElements = [];
    requiredElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ Element found: ${elementId}`);
        } else {
            console.error(`❌ Element missing: ${elementId}`);
            missingElements.push(elementId);
        }
    });
    
    if (missingElements.length > 0) {
        console.error(`❌ Missing ${missingElements.length} required elements`);
        return false;
    }
    
    // Test 6: Test PDF content generation
    console.log('\n📄 Testing PDF Content Generation:');
    
    try {
        const pdfContent = createPDFContent();
        if (pdfContent && pdfContent.length > 0) {
            console.log('✅ PDF content generated successfully');
            console.log(`📊 Content length: ${pdfContent.length} characters`);
            
            // Check for required sections
            const requiredSections = [
                'Server Management Pricing Calculator',
                'Pricing Model Notice',
                'Support Agreement Terms',
                'Servers',
                'Websites & Domains',
                'One-Time Services',
                'Monthly Services',
                'Pricing Summary'
            ];
            
            let missingSections = [];
            requiredSections.forEach(section => {
                if (pdfContent.includes(section) || pdfContent.includes(section.replace(/&/g, '&amp;'))) {
                    console.log(`✅ Section found: ${section}`);
                } else {
                    console.warn(`⚠️ Section might be missing: ${section}`);
                    missingSections.push(section);
                }
            });
            
            if (missingSections.length > 0) {
                console.warn(`⚠️ ${missingSections.length} sections might be missing or have different text`);
            }
        } else {
            console.error('❌ PDF content generation failed');
            return false;
        }
    } catch (error) {
        console.error('❌ Error generating PDF content:', error);
        return false;
    }
    
    // Test 7: Check CSS classes for PDF styling
    console.log('\n🎨 Testing PDF CSS Classes:');
    
    const requiredCSSClasses = [
        'pdf-content',
        'pdf-page-break',
        'pdf-avoid-break',
        'pdf-section',
        'pdf-pricing-summary'
    ];
    
    const styleSheets = document.styleSheets;
    let foundClasses = [];
    
    for (let i = 0; i < styleSheets.length; i++) {
        try {
            const rules = styleSheets[i].cssRules || styleSheets[i].rules;
            for (let j = 0; j < rules.length; j++) {
                const rule = rules[j];
                if (rule.selectorText) {
                    requiredCSSClasses.forEach(className => {
                        if (rule.selectorText.includes(`.${className}`)) {
                            foundClasses.push(className);
                        }
                    });
                }
            }
        } catch (e) {
            // Skip external stylesheets that can't be accessed
        }
    }
    
    requiredCSSClasses.forEach(className => {
        if (foundClasses.includes(className)) {
            console.log(`✅ CSS class found: ${className}`);
        } else {
            console.warn(`⚠️ CSS class not found: ${className}`);
        }
    });
    
    console.log('\n🎉 PDF Generation Validation Complete!');
    console.log('✅ All core functionality appears to be working correctly.');
    console.log('\n📋 Next Steps:');
    console.log('1. Test PDF generation with different configurations');
    console.log('2. Verify page breaks work correctly');
    console.log('3. Test both English and Lithuanian languages');
    console.log('4. Validate filename timestamps are unique');
    
    return true;
}

// Auto-run validation if script is loaded
if (typeof window !== 'undefined') {
    console.log('🚀 PDF Validation Script Loaded');
    console.log('Run validatePDFGeneration() to test PDF functionality');
}
