<!DOCTYPE html>
<html>
<head>
    <title>Console <PERSON>r Test</title>
    <script>
        window.addEventListener('load', function() {
            console.log('Page loaded successfully');
            
            // Test if external dependencies are available
            if (typeof jspdf !== 'undefined') {
                console.log('jsPDF loaded successfully');
            } else {
                console.error('jsPDF not loaded');
            }
            
            if (typeof html2canvas !== 'undefined') {
                console.log('html2canvas loaded successfully');
            } else {
                console.error('html2canvas not loaded');
            }
            
            // Test if main functions exist
            const functionsToCheck = ['addServer', 'addWebsite', 'updateAllText', 'updateSummary', 'generatePDF'];
            functionsToCheck.forEach(func => {
                if (typeof window[func] === 'function') {
                    console.log(func + ' function exists');
                } else {
                    console.error(func + ' function missing');
                }
            });
            
            // Test translations object
            if (typeof translations !== 'undefined' && translations.en) {
                console.log('Translations object exists and has English translations');
            } else {
                console.error('Translations object missing or malformed');
            }
        });
        
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <h1>Testing Console Errors</h1>
    <p>Check browser console for errors</p>
    
    <script>
        // Simulate the key parts of the main script
        const translations = {
            en: {
                mainTitle: "Test Title"
            }
        };
        
        function addServer() {
            console.log('addServer called');
        }
        
        function addWebsite() {
            console.log('addWebsite called');
        }
        
        function updateAllText() {
            console.log('updateAllText called');
        }
        
        function updateSummary() {
            console.log('updateSummary called');
        }
        
        function generatePDF() {
            console.log('generatePDF called');
        }
        
        // Simulate initialization
        console.log('Starting initialization...');
        addServer();
        addWebsite();
        updateAllText();
        updateSummary();
        console.log('Initialization complete');
    </script>
</body>
</html>