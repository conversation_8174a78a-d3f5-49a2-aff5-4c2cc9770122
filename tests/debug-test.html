<!DOCTYPE html>
<html>
<head>
    <title>Debug Test - BSBD Calculator</title>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="output"></div>
    <script>
        console.log("Debug test starting...");
        
        // Test if we can access the original HTML file content
        fetch('./index.html')
            .then(response => response.text())
            .then(html => {
                console.log("HTML file length:", html.length);
                
                // Check if key functions exist in the HTML
                const hasAddServer = html.includes('function addServer()');
                const hasAddWebsite = html.includes('function addWebsite()');
                const hasCloudProviders = html.includes('const cloudProviders');
                const hasInitialization = html.includes('addServer();') && html.includes('addWebsite();');
                
                const output = document.getElementById('output');
                output.innerHTML = `
                    <p>HTML file loaded: ${html.length > 0 ? 'Yes' : 'No'}</p>
                    <p>addServer function found: ${hasAddServer ? 'Yes' : 'No'}</p>
                    <p>addWebsite function found: ${hasAddWebsite ? 'Yes' : 'No'}</p>
                    <p>cloudProviders defined: ${hasCloudProviders ? 'Yes' : 'No'}</p>
                    <p>Initialization code found: ${hasInitialization ? 'Yes' : 'No'}</p>
                `;
            })
            .catch(error => {
                console.error("Error loading HTML:", error);
                document.getElementById('output').innerHTML = `<p>Error: ${error.message}</p>`;
            });
    </script>
</body>
</html>