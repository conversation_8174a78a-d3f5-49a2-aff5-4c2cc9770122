<!DOCTYPE html>
<html>
<head>
    <title>Debug Initialization</title>
    <style>
        .server-item { border: 1px solid #ccc; padding: 10px; margin: 10px; }
        .remove-btn { background: red; color: white; padding: 5px; }
    </style>
</head>
<body>
    <h1>Debug Initialization Test</h1>
    
    <div id="servers-container"></div>
    <button onclick="addServer()">Add Server</button>
    
    <div id="websites-container"></div>
    <button onclick="addWebsite()">Add Website</button>
    
    <div id="console-output"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        function log(message) {
            console.log(message);
            document.getElementById('console-output').innerHTML += '<p>' + message + '</p>';
        }
        
        // Simplified translations
        const translations = {
            en: {
                serverLabel: "Server",
                removeBtn: "Remove",
                cloudProviderLabel: "Cloud Provider",
                ramLabel: "RAM",
                migrationLabel: "Migration",
                websiteLabel: "Website"
            }
        };
        
        const cloudProviders = {
            'hetzner': { name: 'Hetzner', extraFee: 0 },
            'vultr': { name: 'Vultr', extraFee: 0 }
        };
        
        let serverCount = 0;
        let websiteCount = 0;
        
        function updateSummary() {
            log('updateSummary called');
        }
        
        function updateAllText() {
            log('updateAllText called');
        }
        
        function removeServer(id) {
            log('removeServer called for ' + id);
            const element = document.getElementById(`server-${id}`);
            if (element) {
                element.remove();
            }
        }
        
        function updateRAM(serverId, change) {
            log('updateRAM called');
        }
        
        function updateRAMDisplay(serverId) {
            log('updateRAMDisplay called');
        }
        
        function toggleServerServices(serverId) {
            log('toggleServerServices called');
        }
        
        function addServer() {
            log('addServer() called - START');
            try {
                serverCount++;
                const container = document.getElementById('servers-container');
                
                if (!container) {
                    log('ERROR: servers-container not found!');
                    return;
                }
                
                log('Container found, creating server ' + serverCount);
                
                const serverDiv = document.createElement('div');
                serverDiv.className = 'server-item';
                serverDiv.id = `server-${serverCount}`;
                
                const t = translations.en;
                
                serverDiv.innerHTML = `
                    <div class="server-header">
                        <h3>${t.serverLabel} ${serverCount}</h3>
                        ${serverCount > 1 ? '<button class="remove-btn" onclick="removeServer(' + serverCount + ')">' + t.removeBtn + '</button>' : ''}
                    </div>
                    <div>
                        <label>${t.cloudProviderLabel}</label>
                        <select id="provider-${serverCount}" onchange="updateSummary()">
                            ${Object.entries(cloudProviders).map(([key, provider]) =>
                                `<option value="${key}" ${key === 'hetzner' ? 'selected' : ''}>${provider.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div>
                        <label>${t.ramLabel}</label>
                        <input type="range" id="ram-slider-${serverCount}" min="1" max="64" value="1" oninput="updateRAMDisplay(${serverCount})">
                        <span id="ram-value-${serverCount}">1 GB</span>
                    </div>
                    <div>
                        <label>
                            <input type="checkbox" id="migration-${serverCount}" onchange="updateSummary()">
                            ${t.migrationLabel}
                        </label>
                    </div>
                `;
                
                container.appendChild(serverDiv);
                log('Server ' + serverCount + ' added successfully');
                updateSummary();
                log('addServer() called - END');
            } catch (error) {
                log('ERROR in addServer: ' + error.message);
                console.error('Error in addServer:', error);
            }
        }
        
        function addWebsite() {
            log('addWebsite() called - START');
            try {
                websiteCount++;
                const container = document.getElementById('websites-container');
                
                if (!container) {
                    log('ERROR: websites-container not found!');
                    return;
                }
                
                log('Container found, creating website ' + websiteCount);
                
                const websiteDiv = document.createElement('div');
                websiteDiv.className = 'website-item';
                websiteDiv.id = `website-${websiteCount}`;
                
                const t = translations.en;
                
                websiteDiv.innerHTML = `
                    <div>
                        <h3>${t.websiteLabel} ${websiteCount}</h3>
                        ${websiteCount > 1 ? '<button class="remove-btn" onclick="removeWebsite(' + websiteCount + ')">' + t.removeBtn + '</button>' : ''}
                        <input type="text" placeholder="Domain name">
                    </div>
                `;
                
                container.appendChild(websiteDiv);
                log('Website ' + websiteCount + ' added successfully');
                updateSummary();
                log('addWebsite() called - END');
            } catch (error) {
                log('ERROR in addWebsite: ' + error.message);
                console.error('Error in addWebsite:', error);
            }
        }
        
        function removeWebsite(id) {
            log('removeWebsite called for ' + id);
            const element = document.getElementById(`website-${id}`);
            if (element) {
                element.remove();
            }
        }
        
        // Test initialization
        log('Starting initialization...');
        
        window.addEventListener('load', function() {
            log('Window loaded, running initialization');
            
            try {
                log('Calling addServer()...');
                addServer();
                
                log('Calling addWebsite()...');
                addWebsite();
                
                log('Calling updateAllText()...');
                updateAllText();
                
                log('Calling updateSummary()...');
                updateSummary();
                
                log('Initialization complete!');
            } catch (error) {
                log('ERROR during initialization: ' + error.message);
                console.error('Initialization error:', error);
            }
        });
        
        log('Script loaded');
    </script>
</body>
</html>