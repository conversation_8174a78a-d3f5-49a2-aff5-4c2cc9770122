<!DOCTYPE html>
<html>
<head>
    <title>Test Initialization Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .waiting { border-color: #ffc107; background: #fff3cd; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn { background: #007bff; color: white; }
        .test-btn:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>BSBD Calculator - Initialization Fix Test</h1>
    
    <div id="dom-test" class="test-section waiting">
        <h3>DOM Readiness Test</h3>
        <p>Status: <span id="dom-status">Checking...</span></p>
    </div>
    
    <div id="dependencies-test" class="test-section waiting">
        <h3>External Dependencies Test</h3>
        <p>jsPDF: <span id="jspdf-status">Loading...</span></p>
        <p>html2canvas: <span id="html2canvas-status">Loading...</span></p>
    </div>
    
    <div id="functions-test" class="test-section waiting">
        <h3>Core Functions Test</h3>
        <p>Status: <span id="functions-status">Testing...</span></p>
        <div id="test-container"></div>
        <div id="test-websites"></div>
    </div>
    
    <div id="buttons-test" class="test-section">
        <h3>Button Functionality Test</h3>
        <button id="test-add-server" class="test-btn" onclick="testAddServer()">Test Add Server</button>
        <button id="test-add-website" class="test-btn" onclick="testAddWebsite()">Test Add Website</button>
        <button id="test-pdf" class="test-btn" onclick="testPDF()" disabled>Test PDF Generation</button>
    </div>
    
    <div id="console-output" class="test-section">
        <h3>Console Output</h3>
        <div id="log-output" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        // Enhanced logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logOutput = document.getElementById('log-output');
            const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // Simulate the key parts of the main application
        const translations = {
            en: {
                serverLabel: "Server",
                removeBtn: "Remove",
                websiteLabel: "Website"
            }
        };
        
        let serverCount = 0;
        let websiteCount = 0;
        
        function updateSummary() {
            log('updateSummary() called', 'success');
        }
        
        function updateAllText() {
            log('updateAllText() called', 'success');
        }
        
        function testAddServer() {
            try {
                serverCount++;
                const container = document.getElementById('test-container');
                const div = document.createElement('div');
                div.innerHTML = `<p>Server ${serverCount} added successfully</p>`;
                container.appendChild(div);
                log(`Server ${serverCount} added successfully`, 'success');
                updateSummary();
            } catch (error) {
                log(`Error adding server: ${error.message}`, 'error');
            }
        }
        
        function testAddWebsite() {
            try {
                websiteCount++;
                const container = document.getElementById('test-websites');
                const div = document.createElement('div');
                div.innerHTML = `<p>Website ${websiteCount} added successfully</p>`;
                container.appendChild(div);
                log(`Website ${websiteCount} added successfully`, 'success');
                updateSummary();
            } catch (error) {
                log(`Error adding website: ${error.message}`, 'error');
            }
        }
        
        function testPDF() {
            if (typeof jspdf === 'undefined' || typeof html2canvas === 'undefined') {
                log('PDF dependencies not available', 'error');
                return;
            }
            log('PDF generation test - dependencies available', 'success');
        }
        
        // Test DOM readiness
        function testDOMReadiness() {
            log('Testing DOM readiness...');
            
            if (document.readyState === 'complete') {
                document.getElementById('dom-status').textContent = 'Complete';
                document.getElementById('dom-test').className = 'test-section success';
                log('DOM is ready', 'success');
            } else {
                document.getElementById('dom-status').textContent = 'Loading...';
                setTimeout(testDOMReadiness, 100);
            }
        }
        
        // Test dependencies
        function testDependencies() {
            log('Testing external dependencies...');
            
            const checkInterval = setInterval(() => {
                // Check jsPDF
                if (typeof jspdf !== 'undefined') {
                    document.getElementById('jspdf-status').textContent = 'Loaded ✓';
                    document.getElementById('jspdf-status').style.color = '#28a745';
                } else {
                    document.getElementById('jspdf-status').textContent = 'Loading...';
                }
                
                // Check html2canvas
                if (typeof html2canvas !== 'undefined') {
                    document.getElementById('html2canvas-status').textContent = 'Loaded ✓';
                    document.getElementById('html2canvas-status').style.color = '#28a745';
                } else {
                    document.getElementById('html2canvas-status').textContent = 'Loading...';
                }
                
                // If both loaded
                if (typeof jspdf !== 'undefined' && typeof html2canvas !== 'undefined') {
                    clearInterval(checkInterval);
                    document.getElementById('dependencies-test').className = 'test-section success';
                    document.getElementById('test-pdf').disabled = false;
                    log('All dependencies loaded successfully', 'success');
                }
            }, 100);
            
            // Timeout after 10 seconds
            setTimeout(() => {
                clearInterval(checkInterval);
                if (typeof jspdf === 'undefined' || typeof html2canvas === 'undefined') {
                    document.getElementById('dependencies-test').className = 'test-section error';
                    log('Some dependencies failed to load', 'error');
                }
            }, 10000);
        }
        
        // Test core functions
        function testCoreFunctions() {
            log('Testing core functions...');
            
            try {
                // Test basic functionality
                testAddServer();
                testAddWebsite();
                updateAllText();
                
                document.getElementById('functions-status').textContent = 'All functions working ✓';
                document.getElementById('functions-status').style.color = '#28a745';
                document.getElementById('functions-test').className = 'test-section success';
                log('Core functions test completed successfully', 'success');
                
            } catch (error) {
                document.getElementById('functions-status').textContent = 'Error: ' + error.message;
                document.getElementById('functions-status').style.color = '#dc3545';
                document.getElementById('functions-test').className = 'test-section error';
                log(`Core functions test failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize tests
        log('Starting initialization tests...');
        
        // Test when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                log('DOMContentLoaded event fired');
                testDOMReadiness();
                testDependencies();
                testCoreFunctions();
            });
        } else {
            log('DOM already ready');
            testDOMReadiness();
            testDependencies();
            testCoreFunctions();
        }
        
        // Also test on window load
        window.addEventListener('load', function() {
            log('Window load event fired');
        });
        
        log('Test script loaded successfully');
    </script>
</body>
</html>