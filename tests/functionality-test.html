<!DOCTYPE html>
<html>
<head>
    <title>BSBD Calculator Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .pass { color: green; }
        .fail { color: red; }
        .warning { color: orange; }
        .test-result { margin: 5px 0; }
        #test-frame { width: 100%; height: 400px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>BSBD Calculator Functionality Test Report</h1>
    
    <div class="test-section">
        <h2>Test Environment</h2>
        <iframe id="test-frame" src="index.html"></iframe>
        <div>
            <button onclick="testAddServerButton()">Test Add Server Button</button>
            <button onclick="testAddWebsiteButton()">Test Add Website Button</button>
            <button onclick="testAnnualBilling()">Test Annual Billing Toggle</button>
            <button onclick="testInitialization()">Test Initialization</button>
            <button onclick="runAllTests()">Run All Tests</button>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        let testFrame;
        let testResults = [];

        function waitForFrameLoad() {
            return new Promise((resolve) => {
                testFrame = document.getElementById('test-frame');
                if (testFrame.contentDocument.readyState === 'complete') {
                    resolve();
                } else {
                    testFrame.onload = resolve;
                }
            });
        }

        function logResult(test, status, message) {
            testResults.push({ test, status, message });
            updateResults();
        }

        function updateResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
        }

        async function testAddServerButton() {
            await waitForFrameLoad();
            try {
                const doc = testFrame.contentDocument;
                const addServerBtn = doc.getElementById('add-server-btn');
                const serversContainer = doc.getElementById('servers-container');
                
                if (!addServerBtn) {
                    logResult('Add Server Button', 'fail', 'Button element not found');
                    return;
                }

                const initialServerCount = serversContainer.children.length;
                
                // Try to click the button
                addServerBtn.click();
                
                // Wait a moment for DOM to update
                setTimeout(() => {
                    const newServerCount = serversContainer.children.length;
                    if (newServerCount > initialServerCount) {
                        logResult('Add Server Button', 'pass', `Successfully added server (${initialServerCount} → ${newServerCount})`);
                    } else {
                        logResult('Add Server Button', 'fail', `No server added (count: ${newServerCount})`);
                    }
                }, 100);
                
            } catch (error) {
                logResult('Add Server Button', 'fail', `Error: ${error.message}`);
            }
        }

        async function testAddWebsiteButton() {
            await waitForFrameLoad();
            try {
                const doc = testFrame.contentDocument;
                const addWebsiteBtn = doc.getElementById('add-website-btn');
                const websitesContainer = doc.getElementById('websites-container');
                
                if (!addWebsiteBtn) {
                    logResult('Add Website Button', 'fail', 'Button element not found');
                    return;
                }

                const initialWebsiteCount = websitesContainer.children.length;
                
                // Try to click the button
                addWebsiteBtn.click();
                
                // Wait a moment for DOM to update
                setTimeout(() => {
                    const newWebsiteCount = websitesContainer.children.length;
                    if (newWebsiteCount > initialWebsiteCount) {
                        logResult('Add Website Button', 'pass', `Successfully added website (${initialWebsiteCount} → ${newWebsiteCount})`);
                    } else {
                        logResult('Add Website Button', 'fail', `No website added (count: ${newWebsiteCount})`);
                    }
                }, 100);
                
            } catch (error) {
                logResult('Add Website Button', 'fail', `Error: ${error.message}`);
            }
        }

        async function testAnnualBilling() {
            await waitForFrameLoad();
            try {
                const doc = testFrame.contentDocument;
                const annualBillingCheckbox = doc.getElementById('annual-billing');
                
                if (!annualBillingCheckbox) {
                    logResult('Annual Billing Toggle', 'fail', 'Checkbox element not found');
                    return;
                }

                const initialState = annualBillingCheckbox.checked;
                
                // Try to toggle the checkbox
                annualBillingCheckbox.click();
                
                setTimeout(() => {
                    const newState = annualBillingCheckbox.checked;
                    if (newState !== initialState) {
                        logResult('Annual Billing Toggle', 'pass', `Successfully toggled (${initialState} → ${newState})`);
                    } else {
                        logResult('Annual Billing Toggle', 'fail', `Toggle didn't work (still ${newState})`);
                    }
                }, 100);
                
            } catch (error) {
                logResult('Annual Billing Toggle', 'fail', `Error: ${error.message}`);
            }
        }

        async function testInitialization() {
            await waitForFrameLoad();
            try {
                const doc = testFrame.contentDocument;
                const serversContainer = doc.getElementById('servers-container');
                const websitesContainer = doc.getElementById('websites-container');
                
                const serverCount = serversContainer.children.length;
                const websiteCount = websitesContainer.children.length;
                
                if (serverCount > 0 && websiteCount > 0) {
                    logResult('Initialization', 'pass', `Initial server and website created (servers: ${serverCount}, websites: ${websiteCount})`);
                } else {
                    logResult('Initialization', 'warning', `Missing initial items (servers: ${serverCount}, websites: ${websiteCount})`);
                }
                
                // Check for console errors
                const consoleErrors = doc.defaultView.console.error || [];
                if (consoleErrors.length > 0) {
                    logResult('Console Errors', 'warning', `${consoleErrors.length} console errors detected`);
                } else {
                    logResult('Console Errors', 'pass', 'No console errors detected');
                }
                
            } catch (error) {
                logResult('Initialization', 'fail', `Error: ${error.message}`);
            }
        }

        async function runAllTests() {
            testResults = [];
            updateResults();
            
            // Reload the frame to start fresh
            testFrame.src = testFrame.src;
            await waitForFrameLoad();
            
            await testInitialization();
            await new Promise(resolve => setTimeout(resolve, 200));
            await testAddServerButton();
            await new Promise(resolve => setTimeout(resolve, 200));
            await testAddWebsiteButton();
            await new Promise(resolve => setTimeout(resolve, 200));
            await testAnnualBilling();
        }

        // Run initial test when page loads
        window.onload = async () => {
            await waitForFrameLoad();
            await testInitialization();
        };
    </script>
</body>
</html>