<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>BSBD Calculator PDF Generation Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Timestamp Generation</h2>
        <p>Testing the timestamp-based filename generation:</p>
        <button class="test-button" onclick="testTimestamp()">Test Timestamp</button>
        <div id="timestamp-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: PDF Libraries</h2>
        <p>Testing if jsPDF and html2canvas libraries are accessible:</p>
        <button class="test-button" onclick="testLibraries()">Test Libraries</button>
        <div id="libraries-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Open Main Calculator</h2>
        <p>Open the main calculator to test PDF generation:</p>
        <button class="test-button" onclick="openCalculator()">Open Calculator</button>
    </div>
    
    <div class="test-section">
        <h2>Test Scenarios</h2>
        <p>Test these scenarios in the main calculator:</p>
        <ol>
            <li><strong>Single server (1GB RAM)</strong> with no additional services</li>
            <li><strong>Multiple servers (2-3 servers)</strong> with different RAM configurations</li>
            <li><strong>Maximum capacity scenario</strong> (server with 10+ websites to trigger capacity warnings)</li>
            <li><strong>Complex configuration</strong> with multiple servers, websites, and various services enabled</li>
            <li><strong>All services enabled</strong> scenario to test maximum content length</li>
            <li><strong>Both English and Lithuanian</strong> language versions</li>
        </ol>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        function testTimestamp() {
            const now = new Date();
            const timestamp = now.getFullYear() + '-' + 
                            String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                            String(now.getDate()).padStart(2, '0') + '-' + 
                            String(now.getHours()).padStart(2, '0') + 
                            String(now.getMinutes()).padStart(2, '0') + 
                            String(now.getSeconds()).padStart(2, '0');
            const filename = `server-management-quote-${timestamp}.pdf`;
            
            document.getElementById('timestamp-result').innerHTML = `
                <p class="success">✓ Timestamp generated successfully!</p>
                <p><strong>Generated filename:</strong> ${filename}</p>
                <p><strong>Format:</strong> server-management-quote-YYYY-MM-DD-HHMMSS.pdf</p>
            `;
        }
        
        function testLibraries() {
            const resultDiv = document.getElementById('libraries-result');
            let results = [];
            
            // Test jsPDF
            if (typeof jspdf !== 'undefined' && jspdf.jsPDF) {
                results.push('<p class="success">✓ jsPDF library loaded successfully</p>');
            } else {
                results.push('<p class="error">✗ jsPDF library not found</p>');
            }
            
            // Test html2canvas
            if (typeof html2canvas !== 'undefined') {
                results.push('<p class="success">✓ html2canvas library loaded successfully</p>');
            } else {
                results.push('<p class="error">✗ html2canvas library not found</p>');
            }
            
            resultDiv.innerHTML = results.join('');
        }
        
        function openCalculator() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
