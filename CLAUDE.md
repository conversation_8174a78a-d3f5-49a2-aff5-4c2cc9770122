# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Server Management Pricing Calculator** - a single-page web application for calculating professional hosting management service prices. The entire application is contained in one `index.html` file.

## Development Commands

Since this is a static HTML file with no build process:
- **Run locally**: Open `index.html` directly in a web browser
- **Test changes**: Refresh the browser after editing
- **Deploy**: Upload `index.html` to any static hosting service

## Architecture

### Single-File Structure
The entire application exists in `index.html` with three main sections:
1. **HTML**: Lines 1-912 - UI structure and content
2. **CSS**: Lines 7-532 - Embedded styles in `<style>` tag
3. **JavaScript**: Lines 913-1801 - Application logic in `<script>` tag

### Key Components

#### Translation System (lines 915-1078)
- Bilingual support: English (`en`) and Lithuanian (`lt`)
- All UI text stored in `translations` object
- Language switcher updates DOM elements via `data-translate` attributes

#### Pricing Logic
- **Base calculation**: €6 per GB RAM per month (line 919)
- **VAT**: 23% toggle option
- **Annual discount**: 12% for yearly billing
- **Server pricing**: Dynamic based on RAM (1-64 GB)
- **Service pricing**: Fixed prices for one-time and monthly services

#### State Management
Global variables track:
- `servers`: Array of server configurations
- `selectedServices`: Object tracking service selections
- `currentLang`: Active language
- `includeVat`: VAT toggle state
- `annualBilling`: Annual billing toggle

#### External Dependencies
- **jsPDF** (v2.5.1): PDF generation
- **html2canvas** (v1.4.1): HTML to canvas conversion for PDFs
- Both loaded from CDN (lines 910-911)

### Important Functions
- `updatePricing()`: Recalculates all prices when configuration changes
- `generatePDF()`: Creates downloadable quote PDF
- `addServer()/removeServer()`: Manages server list
- `changeLanguage()`: Handles language switching

## Working with This Codebase

### Adding New Features
1. For new UI elements, add HTML structure with appropriate `data-translate` attributes
2. Add translations to both `en` and `lt` objects in the translations section
3. Update pricing calculations in `updatePricing()` if needed
4. Ensure new elements are properly styled (mobile-responsive)

### Modifying Prices
- Server base price: Update line 919 and pricing calculation in `updatePricing()`
- Service prices: Update in translations object and service definitions (lines 533-865)
- VAT rate: Search for `0.23` and `1.23` to find all VAT calculations

### PDF Generation
The PDF export uses a specific layout:
- Captures the entire pricing summary section
- Includes a header with company branding
- Formats prices with proper currency display
- Check `generatePDF()` function for customization